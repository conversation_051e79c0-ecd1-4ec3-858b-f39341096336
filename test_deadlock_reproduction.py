#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to reproduce the deadlock issue in YouTube Recon.
This script uses real data from the log file to create realistic test scenarios.
"""

import asyncio
import json
import logging
import re
import time
from typing import Dict, List, Optional, Tuple
from urllib3.util import parse_url, Url

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the models we need
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from youtube_recon.model.ytdlp import VideoDataByYtDlp
from youtube_recon.impl.gemini_video_recon import VideoSummary
from quota_supervisor_fixed import QuotaSupervisor


def parse_log_data(log_file_path: str) -> List[Dict[str, any]]:
    """Parse the log file to extract video data"""
    videos = []
    current_video = {}
    
    with open(log_file_path, 'r') as f:
        content = f.read()
    
    # Split by video entries (look for id field as separator)
    video_blocks = re.split(r"field_name: 'id'\s*\n\s*data_dict\[ic\(field_name\)\]:", content)
    
    for i, block in enumerate(video_blocks):
        if i == 0:  # Skip the first empty block
            continue
            
        video_data = {}
        lines = block.split('\n')
        
        # Extract video ID from the first line
        id_match = re.search(r"'([^']+)'", lines[0])
        if id_match:
            video_data['id'] = id_match.group(1)
        
        # Parse other fields
        for line in lines[1:]:
            field_match = re.search(r"field_name: '([^']+)'", line)
            if field_match:
                field_name = field_match.group(1)
                # Look for the next line with data
                continue
            
            data_match = re.search(r"data_dict\[ic\(field_name\)\]: (.+)", line)
            if data_match:
                data_str = data_match.group(1)
                try:
                    # Try to evaluate the data (be careful with eval!)
                    if data_str.startswith("'") and data_str.endswith("'"):
                        video_data[field_name] = data_str[1:-1]  # Remove quotes
                    elif data_str.startswith('['):
                        # Handle lists
                        video_data[field_name] = eval(data_str)
                    elif data_str.isdigit():
                        video_data[field_name] = int(data_str)
                    else:
                        video_data[field_name] = data_str
                except:
                    video_data[field_name] = data_str
        
        if video_data:
            videos.append(video_data)
    
    return videos


def create_test_video_data() -> List[Dict[str, any]]:
    """Create test video data based on the log file structure"""
    return [
        {
            'id': 'XHzOzxCQ7MU',
            'webpage_url': 'https://www.youtube.com/watch?v=XHzOzxCQ7MU',
            'fulltitle': 'Selecting a free 3D CAD option - 3D design for 3D printing pt1',
            'description': 'To get the best out of 3D printing, it helps if you can design your own parts. In this tutorial series, we will learn to use a free 3D CAD program to do just that.',
            'tags': ['3d printing', 'cad', 'design', 'tutorial'],
            'categories': ['Education'],
            'duration': 911,
            'uploader': 'Teaching Tech',
            'channel': 'Teaching Tech',
            'timestamp': 1658858412
        },
        {
            'id': 'VvpQu2rsH3A',
            'webpage_url': 'https://www.youtube.com/watch?v=VvpQu2rsH3A',
            'fulltitle': 'Tool holder using Onshape sketches and extrusion - 3D design for 3D printing pt2',
            'description': 'To get the best out of 3D printing, it helps if you can design your own parts. In this tutorial series, we will learn to use a free 3D CAD program to do just that.',
            'tags': ['3d printing', 'onshape', 'sketches', 'extrusion'],
            'categories': ['Education'],
            'duration': 888,
            'uploader': 'Teaching Tech',
            'channel': 'Teaching Tech',
            'timestamp': 1659117620
        },
        # Add more test videos to simulate the 30-video scenario
        *[{
            'id': f'TEST{i:03d}',
            'webpage_url': f'https://www.youtube.com/watch?v=TEST{i:03d}',
            'fulltitle': f'Test Video {i} - Simulated Content',
            'description': f'This is test video {i} for deadlock reproduction testing.',
            'tags': ['test', 'simulation', f'video{i}'],
            'categories': ['Education'],
            'duration': 300 + i * 10,
            'uploader': 'Test Channel',
            'channel': 'Test Channel',
            'timestamp': 1658858412 + i * 3600
        } for i in range(3, 31)]  # Create videos 3-30
    ]


class MockGeminiClient:
    """Mock Gemini client that simulates API behavior including hangs"""
    
    def __init__(self, hang_probability: float = 0.1, hang_duration: float = 30.0):
        self.hang_probability = hang_probability
        self.hang_duration = hang_duration
        self.call_count = 0
    
    async def generate_content(self, model: str, contents: str, config: dict):
        """Simulate Gemini API call with potential hangs"""
        self.call_count += 1
        call_id = self.call_count
        
        logger.info(f"Mock API call {call_id} started")
        
        # Simulate random hangs
        import random
        if random.random() < self.hang_probability:
            logger.warning(f"Mock API call {call_id} hanging for {self.hang_duration}s")
            await asyncio.sleep(self.hang_duration)
            raise asyncio.TimeoutError(f"Mock API call {call_id} timed out")
        
        # Normal processing time
        processing_time = random.uniform(0.5, 3.0)
        await asyncio.sleep(processing_time)
        
        logger.info(f"Mock API call {call_id} completed in {processing_time:.2f}s")
        
        # Return mock response
        class MockResponse:
            def __init__(self):
                self.ok = True
                self.text = json.dumps({
                    "summary": f"This is a mock summary for call {call_id}",
                    "key_points": [f"Point 1 for call {call_id}", f"Point 2 for call {call_id}"],
                    "topics": [f"Topic A for call {call_id}"],
                    "sentiment": "neutral"
                })
        
        return MockResponse()


class MockGeminiVideoRecon:
    """Mock version of GeminiVideoRecon for testing"""
    
    def __init__(self, max_cap: int = 30, time_frame: int = 60, timeout: int = 10, 
                 hang_probability: float = 0.1):
        self.quota_supervisor = QuotaSupervisor(max_cap=max_cap, time_frame=time_frame, lock_timeout=5.0)
        self.client = MockGeminiClient(hang_probability=hang_probability, hang_duration=timeout * 0.5)
        self.timeout = timeout
        self.rate_limit = 1
    
    async def async_get_video_summary(self, url: Url, prop: Optional[VideoDataByYtDlp] = None) -> VideoSummary:
        """Mock implementation of async_get_video_summary"""
        logger.debug(f"Getting summary for {url}")
        
        try:
            async with self.quota_supervisor.async_consume(rate_limit=self.rate_limit):
                logger.debug(f"Calling mock Gemini API for {url}")
                
                # Simulate the API call with timeout
                response = await asyncio.wait_for(
                    self.client.generate_content(
                        model="mock-model",
                        contents=f"Mock content for {url}",
                        config={}
                    ),
                    timeout=self.timeout * 0.8
                )
                
                logger.debug(f"Mock Gemini response OK: {response.ok}")
                
                if not response.ok:
                    raise RuntimeError(f"Mock API returned error for {url}")
                
                return VideoSummary(**json.loads(response.text))
                
        except Exception as e:
            logger.error(f"Failed to get video summary for {url}: {e}")
            raise
    
    async def async_get_video_summaries(self, v_sources: Dict[Url, Optional[VideoDataByYtDlp]]) -> Tuple[Dict[Url, VideoSummary], Dict[Url, Exception]]:
        """Mock implementation with concurrency control"""
        logger.info(f"Processing {len(v_sources)} videos")
        
        # Limit concurrency
        max_concurrent = min(10, len(v_sources))
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_wrapper(url: Url, prop: Optional[VideoDataByYtDlp]):
            async with semaphore:
                try:
                    summary = await self.async_get_video_summary(url=url, prop=prop)
                    return url, summary, None
                except Exception as e:
                    return url, None, e
        
        tasks = []
        for url, prop in v_sources.items():
            tasks.append(asyncio.create_task(limited_wrapper(url, prop)))
        
        result, errors = {}, {}
        
        try:
            # Overall timeout
            overall_timeout = self.timeout * len(v_sources) * 0.1
            finished, pending = await asyncio.wait(
                tasks, 
                return_when=asyncio.ALL_COMPLETED,
                timeout=overall_timeout
            )
            
            for task in finished:
                url, summary, err = task.result()
                if err is None:
                    result[url] = summary
                else:
                    errors[url] = err
            
            # Handle pending tasks
            if pending:
                logger.warning(f"⚠️  {len(pending)} task(s) never finished")
                for task in pending:
                    task.cancel()
                    
        except asyncio.TimeoutError:
            logger.error(f"Overall timeout reached after {overall_timeout}s")
            for task in tasks:
                if not task.done():
                    task.cancel()
        
        return result, errors


async def test_deadlock_reproduction():
    """Test that reproduces the deadlock scenario"""
    logger.info("=== Deadlock Reproduction Test ===")
    
    # Create test data
    test_videos = create_test_video_data()
    logger.info(f"Created {len(test_videos)} test videos")
    
    # Create video sources
    v_sources = {}
    for video_data in test_videos:
        url = parse_url(video_data['webpage_url'])
        # Create a minimal VideoDataByYtDlp object
        prop = VideoDataByYtDlp(
            id=video_data['id'],
            webpage_url=video_data['webpage_url'],
            fulltitle=video_data['fulltitle'],
            description=video_data['description'],
            duration=video_data['duration']
        )
        v_sources[url] = prop
    
    # Create mock recon with high hang probability to reproduce the issue
    mock_recon = MockGeminiVideoRecon(
        max_cap=30,
        time_frame=60,
        timeout=10,
        hang_probability=0.2  # 20% chance of hanging
    )
    
    start_time = time.time()
    
    try:
        logger.info("Starting video summary processing...")
        summaries, errors = await mock_recon.async_get_video_summaries(v_sources)
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Processing completed in {duration:.2f}s")
        logger.info(f"Successful: {len(summaries)}, Failed: {len(errors)}")
        
        # Check if we reproduced the deadlock (indicated by very long duration)
        if duration > 60:  # If it takes more than 60 seconds
            logger.warning("⚠️  Potential deadlock detected - processing took too long")
            return False
        else:
            logger.info("✅ No deadlock detected - processing completed in reasonable time")
            return True
            
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return False


async def main():
    """Run the deadlock reproduction test"""
    logger.info("Starting deadlock reproduction tests")
    
    # Test with the fixed quota supervisor
    success = await test_deadlock_reproduction()
    
    if success:
        logger.info("✅ Test completed successfully - no deadlock detected")
        logger.info("The fix appears to be working correctly!")
    else:
        logger.error("❌ Test failed or deadlock detected")
        logger.error("The issue may still exist or the test needs adjustment")
    
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        exit(1)
