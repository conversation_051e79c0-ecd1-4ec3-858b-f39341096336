#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple script to run all deadlock tests and provide clear results.
"""

import asyncio
import subprocess
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_test_script(script_name: str) -> bool:
    """Run a test script and return success status"""
    logger.info(f"Running {script_name}...")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✅ {script_name} PASSED")
            return True
        else:
            logger.error(f"❌ {script_name} FAILED")
            logger.error(f"STDOUT: {result.stdout}")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {script_name} TIMED OUT (possible deadlock)")
        return False
    except Exception as e:
        logger.error(f"❌ {script_name} failed with exception: {e}")
        return False


def main():
    """Run all tests and provide summary"""
    logger.info("🧪 Running YouTube Recon Deadlock Tests")
    logger.info("=" * 50)
    
    tests = [
        ("test_quota_supervisor_deadlock.py", "QuotaSupervisor Deadlock Test"),
        ("test_deadlock_fix.py", "Basic Deadlock Fix Test"),
    ]
    
    results = []
    
    for script, description in tests:
        logger.info(f"\n📋 {description}")
        logger.info("-" * 30)
        success = run_test_script(script)
        results.append((description, success))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{description}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("The deadlock fix is working correctly.")
        logger.info("\nNext steps:")
        logger.info("1. Test with a small playlist (3-5 videos)")
        logger.info("2. If successful, test with your larger playlist")
        logger.info("\nExample command:")
        logger.info("python -m youtube_recon.impl.gemini_video_recon_cli playlist summaries -u 'YOUR_PLAYLIST_URL'")
        return True
    else:
        logger.warning(f"\n⚠️  {total - passed} test(s) failed.")
        logger.warning("Please review the test output above.")
        return False


if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        exit(1)
