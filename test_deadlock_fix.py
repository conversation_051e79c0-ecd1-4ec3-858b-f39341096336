#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify the deadlock fix for YouTube Recon.
This script tests the QuotaSupervisor improvements in isolation.
"""

import asyncio
import time
import logging
from typing import List
from quota_supervisor_fixed import QuotaSupervisor

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def simulate_api_call(call_id: int, duration: float, quota_supervisor: QuotaSupervisor):
    """Simulate an API call that takes some time"""
    logger.info(f"Task {call_id}: Starting")
    
    try:
        async with quota_supervisor.async_consume(rate_limit=1):
            logger.info(f"Task {call_id}: Got quota, simulating API call for {duration}s")
            await asyncio.sleep(duration)
            logger.info(f"Task {call_id}: API call completed")
            return f"Result from task {call_id}"
    except Exception as e:
        logger.error(f"Task {call_id}: Failed with error: {e}")
        raise

async def test_concurrent_requests(num_tasks: int = 10, api_duration: float = 2.0):
    """Test multiple concurrent requests to simulate the deadlock scenario"""
    logger.info(f"Testing {num_tasks} concurrent requests with {api_duration}s API calls")
    
    # Create quota supervisor with settings similar to your use case
    quota_supervisor = QuotaSupervisor(
        max_cap=30,
        time_frame=60,
        lock_timeout=10.0  # Shorter timeout for testing
    )
    
    # Create tasks
    tasks = []
    for i in range(num_tasks):
        task = asyncio.create_task(simulate_api_call(i, api_duration, quota_supervisor))
        tasks.append(task)
    
    start_time = time.time()
    
    try:
        # Wait for all tasks with a reasonable timeout
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=60.0  # Overall timeout
        )
        
        end_time = time.time()
        logger.info(f"All tasks completed in {end_time - start_time:.2f}s")
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, str))
        failed = sum(1 for r in results if isinstance(r, Exception))
        
        logger.info(f"Results: {successful} successful, {failed} failed")
        
        return successful, failed
        
    except asyncio.TimeoutError:
        logger.error("Test timed out - this indicates a potential deadlock!")
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        return 0, num_tasks

async def test_lock_timeout():
    """Test that lock timeout works correctly"""
    logger.info("Testing lock timeout functionality")
    
    quota_supervisor = QuotaSupervisor(
        max_cap=1,
        time_frame=60,
        lock_timeout=2.0  # Very short timeout
    )
    
    async def long_running_task():
        async with quota_supervisor.async_consume(rate_limit=1):
            logger.info("Long task: Holding lock for 10 seconds")
            await asyncio.sleep(10)
    
    async def quick_task():
        await asyncio.sleep(1)  # Let the long task acquire the lock first
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info("Quick task: Got lock")
                return "success"
        except asyncio.TimeoutError:
            logger.info("Quick task: Timed out as expected")
            return "timeout"
    
    # Start both tasks
    long_task = asyncio.create_task(long_running_task())
    quick_task = asyncio.create_task(quick_task())
    
    try:
        results = await asyncio.gather(long_task, quick_task, return_exceptions=True)
        logger.info(f"Lock timeout test results: {results}")
        
        # The quick task should timeout, not hang
        return "timeout" in str(results[1])
        
    except Exception as e:
        logger.error(f"Lock timeout test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("Starting deadlock fix tests")
    
    # Test 1: Basic concurrent requests
    logger.info("\n=== Test 1: Basic Concurrent Requests ===")
    successful, failed = await test_concurrent_requests(num_tasks=5, api_duration=1.0)
    test1_passed = successful == 5 and failed == 0
    logger.info(f"Test 1 {'PASSED' if test1_passed else 'FAILED'}")
    
    # Test 2: Higher concurrency
    logger.info("\n=== Test 2: Higher Concurrency ===")
    successful, failed = await test_concurrent_requests(num_tasks=15, api_duration=0.5)
    test2_passed = successful > 10  # Allow some failures due to quota limits
    logger.info(f"Test 2 {'PASSED' if test2_passed else 'FAILED'}")
    
    # Test 3: Lock timeout
    logger.info("\n=== Test 3: Lock Timeout ===")
    test3_passed = await test_lock_timeout()
    logger.info(f"Test 3 {'PASSED' if test3_passed else 'FAILED'}")
    
    # Summary
    all_passed = test1_passed and test2_passed and test3_passed
    logger.info(f"\n=== SUMMARY ===")
    logger.info(f"All tests {'PASSED' if all_passed else 'FAILED'}")
    
    if all_passed:
        logger.info("✅ The deadlock fix appears to be working correctly!")
        logger.info("You can now proceed to test with your YouTube playlist.")
    else:
        logger.warning("⚠️  Some tests failed. Please review the logs above.")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        exit(1)
