[{"id": "MKVDWeb7GSo", "title": "Time Kills Trades: How Adding Maximum Duration Boosts Trading Profits", "microformat": {"playerMicroformatRenderer": {"thumbnail": {"thumbnails": [{"url": "https://i.ytimg.com/vi/MKVDWeb7GSo/hqdefault.jpg", "width": 480, "height": 360}]}, "embed": {"iframeUrl": "https://www.youtube.com/embed/MKVDWeb7GSo", "width": 1280, "height": 720}, "title": {"simpleText": "Time Kills Trades: How Adding Maximum Duration Boosts Trading Profits"}, "description": {"simpleText": "What’s the optimal approach to balance waiting for a trade to reach the profit target vs exiting if the price action is not moving as fast as expected? Let’s find out!\n\n 👉View the backtest on ROC strategy that I’m improving here: https://youtu.be/afbo3rnmaX0\n\n 👉  Get the indicators for ThinkOrSwim: https://github.com/seriousbacktester/ThinkOrSwim_Indicator_Library\n\n 👉Watch more backtesting strategy tests: https://www.youtube.com/playlist?list=PLwIA1j27TVJ389661gglXgQEfMdXQPrPt\n\n 👉Learn how to backtest: https://www.youtube.com/playlist?list=PLwIA1j27TVJ2uXIBFUwqOlTQK5PKa8Amr\n\n 👉Learn more about ThinkOrSwim: https://www.youtube.com/playlist?list=PLwIA1j27TVJ2JVg4QVxGzzfT1uTFSJbsK\n\n👉 Submit a strategy for me to backtest on a future video: https://forms.gle/UeZ2S97MQzSFuRPdA\n\n#backtesting #stocks #trading #thinkorswim #technicalindicators\nNothing here is investment advice"}, "lengthSeconds": "291", "ownerProfileUrl": "http://www.youtube.com/@seriousbacktester", "externalChannelId": "UCkDhTjPbmkM2fmssttIcP_g", "isFamilySafe": true, "availableCountries": ["AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AQ", "AR", "AS", "AT", "AU", "AW", "AX", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BL", "BM", "BN", "BO", "BQ", "BR", "BS", "BT", "BV", "BW", "BY", "BZ", "CA", "CC", "CD", "CF", "CG", "CH", "CI", "CK", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CX", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "EH", "ER", "ES", "ET", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF", "GG", "GH", "GI", "GL", "GM", "GN", "GP", "GQ", "GR", "GS", "GT", "GU", "GW", "GY", "HK", "HM", "HN", "HR", "HT", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IQ", "IR", "IS", "IT", "JE", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KP", "KR", "KW", "KY", "KZ", "LA", "LB", "LC", "LI", "LK", "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "ML", "MM", "MN", "MO", "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PN", "PR", "PS", "PT", "PW", "PY", "QA", "RE", "RO", "RS", "RU", "RW", "SA", "SB", "SC", "SD", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SO", "SR", "SS", "ST", "SV", "SX", "SY", "SZ", "TC", "TD", "TF", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TW", "TZ", "UA", "UG", "UM", "US", "UY", "UZ", "VA", "VC", "VE", "VG", "VI", "VN", "VU", "WF", "WS", "YE", "YT", "ZA", "ZM", "ZW"], "isUnlisted": false, "hasYpcMetadata": false, "viewCount": "964", "category": "Education", "publishDate": "2025-03-23T07:30:14-07:00", "ownerChannelName": "Serious Backtester", "uploadDate": "2025-03-23T07:30:14-07:00", "isShortsEligible": false, "externalVideoId": "MKVDWeb7GSo"}}, "tracks": [{"language": "English (auto-generated)", "transcript": [{"text": "knowing when to give up can actually be", "start": "0.199", "dur": "3.641"}, {"text": "a secret to success in trading", "start": "1.76", "dur": "3.92"}, {"text": "strategies we typically have a profit", "start": "3.84", "dur": "4.24"}, {"text": "Target and a stop loss the trade runs", "start": "5.68", "dur": "4.56"}, {"text": "until one of the other is reached but", "start": "8.08", "dur": "4.559"}, {"text": "what if the price action is stagnant", "start": "10.24", "dur": "4.399"}, {"text": "staying in a trade too long ties up the", "start": "12.639", "dur": "4.241"}, {"text": "money that could be used elsewhere today", "start": "14.639", "dur": "3.8"}, {"text": "I'll show an analysis of how", "start": "16.88", "dur": "3.84"}, {"text": "modification of Maximum trade length as", "start": "18.439", "dur": "5.16"}, {"text": "an additional exit Criterium can take a", "start": "20.72", "dur": "4.92"}, {"text": "trading strategy to the next level", "start": "23.599", "dur": "3.281"}, {"text": "remember nothing here is investment", "start": "25.64", "dur": "2.879"}, {"text": "advice and I am not an investment", "start": "26.88", "dur": "3.92"}, {"text": "adviser if you haven't seen my back test", "start": "28.519", "dur": "4.601"}, {"text": "on my rate of change strategy see the", "start": "30.8", "dur": "4.32"}, {"text": "link below but basically this finds", "start": "33.12", "dur": "3.959"}, {"text": "stocks that have dropped more than 20%", "start": "35.12", "dur": "4.16"}, {"text": "in the last 14 days and then enters a", "start": "37.079", "dur": "4.16"}, {"text": "trade when the rate of change is more", "start": "39.28", "dur": "3.72"}, {"text": "positive than both the day before and", "start": "41.239", "dur": "4.521"}, {"text": "three days before the profit Target and", "start": "43", "dur": "4.879"}, {"text": "stoploss are set as proportions of the", "start": "45.76", "dur": "5.319"}, {"text": "14-day average true range when I back", "start": "47.879", "dur": "6.2"}, {"text": "test strategies I always set a maximum", "start": "51.079", "dur": "5.361"}, {"text": "amount of time to stay in a trade but", "start": "54.079", "dur": "4.081"}, {"text": "historically the reason I've done this", "start": "56.44", "dur": "4.16"}, {"text": "is to treat all trade entries the same", "start": "58.16", "dur": "3.919"}, {"text": "no matter if they appear at the", "start": "60.6", "dur": "3.16"}, {"text": "beginning or the end of the price", "start": "62.079", "dur": "4.241"}, {"text": "history otherwise trade entries at the", "start": "63.76", "dur": "4.679"}, {"text": "start of the data set would have years", "start": "66.32", "dur": "4.28"}, {"text": "of future price data let to let the", "start": "68.439", "dur": "4.521"}, {"text": "trade play out whereas trades closer to", "start": "70.6", "dur": "4"}, {"text": "the present day would have much less", "start": "72.96", "dur": "2.96"}, {"text": "future data to", "start": "74.6", "dur": "4.08"}, {"text": "consider but I would typically choose a", "start": "75.92", "dur": "5.64"}, {"text": "maximum trade duration that would let", "start": "78.68", "dur": "5.439"}, {"text": "almost all trades have a chance to play", "start": "81.56", "dur": "5.72"}, {"text": "out I made two prior videos on this rate", "start": "84.119", "dur": "5.64"}, {"text": "of change strategy and in my first video", "start": "87.28", "dur": "4.56"}, {"text": "I I set the maximum number of days to", "start": "89.759", "dur": "4.481"}, {"text": "stay in a trade is 20 days in the second", "start": "91.84", "dur": "6.44"}, {"text": "video I set 15 bars as the maximum so", "start": "94.24", "dur": "5.839"}, {"text": "obviously the length of time it takes to", "start": "98.28", "dur": "4.76"}, {"text": "make a certain return matters in terms", "start": "100.079", "dur": "6.68"}, {"text": "of profit per day a trade making 5% in 5", "start": "103.04", "dur": "5.52"}, {"text": "days would be more beneficial than a", "start": "106.759", "dur": "5.441"}, {"text": "trade that made 6% in 20 days even", "start": "108.56", "dur": "5"}, {"text": "though the second trade had a greater", "start": "112.2", "dur": "4.48"}, {"text": "Total return it made less much less", "start": "113.56", "dur": "5.36"}, {"text": "profit per day that the money was tied", "start": "116.68", "dur": "4.64"}, {"text": "up of course exiting a trade due to a", "start": "118.92", "dur": "4.64"}, {"text": "certain number of days passing applies", "start": "121.32", "dur": "4.399"}, {"text": "to trades that haven't yet hit the", "start": "123.56", "dur": "4.08"}, {"text": "profit Target or stop-loss the longer", "start": "125.719", "dur": "3.801"}, {"text": "you let the trades run the greater", "start": "127.64", "dur": "3.52"}, {"text": "percentage of those trades will exit", "start": "129.52", "dur": "4.359"}, {"text": "based on the profit and the stops points", "start": "131.16", "dur": "5.56"}, {"text": "that are set the shorter the allowed", "start": "133.879", "dur": "5.041"}, {"text": "maximum trade duration the greater", "start": "136.72", "dur": "4.84"}, {"text": "percentage of Trades will exit based on", "start": "138.92", "dur": "4.56"}, {"text": "the time criteria based on time timing", "start": "141.56", "dur": "5"}, {"text": "out so this is easy to test let's simply", "start": "143.48", "dur": "5.16"}, {"text": "test the strategy with every maximum", "start": "146.56", "dur": "4.84"}, {"text": "trade length from one day to 60 days and", "start": "148.64", "dur": "5.12"}, {"text": "track the total profit per year or maybe", "start": "151.4", "dur": "5.919"}, {"text": "profit per day would make more sense the", "start": "153.76", "dur": "5.44"}, {"text": "percentage of timeouts and the", "start": "157.319", "dur": "4.441"}, {"text": "percentage of wins and losses so here's", "start": "159.2", "dur": "4.2"}, {"text": "a graph of these results and I think", "start": "161.76", "dur": "3.759"}, {"text": "they're quite fascinating in this graph", "start": "163.4", "dur": "5.119"}, {"text": "the maximum trade length allowed trade", "start": "165.519", "dur": "7.681"}, {"text": "length is the xaxis and the y- axis", "start": "168.519", "dur": "8.161"}, {"text": "shows percent profit or percent win rate", "start": "173.2", "dur": "5.36"}, {"text": "percent loss rate and percent timeout", "start": "176.68", "dur": "5.559"}, {"text": "rate so the blue bar is the profit per", "start": "178.56", "dur": "6.599"}, {"text": "year for this strategy if you time out", "start": "182.239", "dur": "5.36"}, {"text": "after a certain number of", "start": "185.159", "dur": "7.201"}, {"text": "candles the red bar is the win rate", "start": "187.599", "dur": "7.961"}, {"text": "meaning the it hits the profit Target", "start": "192.36", "dur": "5.04"}, {"text": "before timing", "start": "195.56", "dur": "6.52"}, {"text": "out and the green line is the uh what", "start": "197.4", "dur": "8"}, {"text": "percentage of Trades do get sold simply", "start": "202.08", "dur": "4.64"}, {"text": "because they hit a certain number of", "start": "205.4", "dur": "2.28"}, {"text": "days the", "start": "206.72", "dur": "4.599"}, {"text": "timeout number of timeout days and the", "start": "207.68", "dur": "7.16"}, {"text": "yellow bar is the loss rate if you", "start": "211.319", "dur": "6.2"}, {"text": "always exit the trade after just one day", "start": "214.84", "dur": "5.479"}, {"text": "then only 18% of the trades will have", "start": "217.519", "dur": "4.44"}, {"text": "hit either the profit Target or the", "start": "220.319", "dur": "5.92"}, {"text": "stop- loss however at 60 days 98% of", "start": "221.959", "dur": "6.56"}, {"text": "Trades have either hit the profit Target", "start": "226.239", "dur": "5.241"}, {"text": "or stop loss so this graph shows us what", "start": "228.519", "dur": "5.881"}, {"text": "is optimal for this strategy of note for", "start": "231.48", "dur": "5.92"}, {"text": "this strategy it is not optimal to time", "start": "234.4", "dur": "6.08"}, {"text": "out the trades in the first 7 days as", "start": "237.4", "dur": "4.399"}, {"text": "you'll get a higher return by letting", "start": "240.48", "dur": "3.56"}, {"text": "the trades run for longer but there's a", "start": "241.799", "dur": "4"}, {"text": "clear Peak that happens at 10 days which", "start": "244.04", "dur": "4.199"}, {"text": "is where the strategy is optimal at a", "start": "245.799", "dur": "4.681"}, {"text": "point where about onethird of the trades", "start": "248.239", "dur": "5.801"}, {"text": "are being sold due to uh timing out at", "start": "250.48", "dur": "5.08"}, {"text": "10 days and not hitting either the", "start": "254.04", "dur": "4.599"}, {"text": "profit Target or the stop-loss with a", "start": "255.56", "dur": "8.199"}, {"text": "win rate of 59% and a loss rate of 7%", "start": "258.639", "dur": "6.84"}, {"text": "there are still pretty good results from", "start": "263.759", "dur": "4.321"}, {"text": "letting it run longer but after 10 days", "start": "265.479", "dur": "5.121"}, {"text": "the number of losses tends to increase", "start": "268.08", "dur": "3.839"}, {"text": "proportionally faster than the", "start": "270.6", "dur": "2.28"}, {"text": "additional", "start": "271.919", "dur": "3.801"}, {"text": "winds so the optimal for this strategy", "start": "272.88", "dur": "6.68"}, {"text": "is to time out at 10 days I would argue", "start": "275.72", "dur": "5.28"}, {"text": "that this type of analysis should be", "start": "279.56", "dur": "3.8"}, {"text": "done on any successful strategy for", "start": "281", "dur": "4.16"}, {"text": "further optimization so I wanted to", "start": "283.36", "dur": "3.399"}, {"text": "share these results in just a quick", "start": "285.16", "dur": "3.759"}, {"text": "video and would appreciate your thoughts", "start": "286.759", "dur": "5.921"}, {"text": "below thanks for watching", "start": "288.919", "dur": "3.761"}]}], "isLive": false, "languages": [{"label": "English (auto-generated)", "languageCode": "en"}], "isLoginRequired": false, "playabilityStatus": {"status": "OK", "playableInEmbed": true, "miniplayer": {"miniplayerRenderer": {"playbackMode": "PLAYBACK_MODE_ALLOW"}}, "contextParams": "Q0FFU0FnZ0M="}, "author": "Serious Backtester", "channelId": "UCkDhTjPbmkM2fmssttIcP_g", "keywords": ["stock", "trading", "backtest", "crypto", "forex", "strategy"]}]