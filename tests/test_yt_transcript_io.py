#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
from mockito import ANY, mock, unstub, verify, when 
import os
import pytest
from pytest import LogCaptureFixture
import random
import re
from typing import List, Dict, Any, Set
from youtube_recon.yt_transcript_io import YtTranscriptIoClient
from youtube_recon.model.yt_transcript_io import TranscriptItem, Track, Language, VideoDetail

# Construct the path to the sample JSON file
SAMPLE_JSON_PATH = os.path.join(os.path.dirname(__file__), 'resource', 'youtube-transcript.io.sample.json')
# Read the sample JSON file
with open(SAMPLE_JSON_PATH, 'r') as f:
    SAMPLE_VIDEO_DETAILS_JSON = f.read()


@pytest.fixture(scope="function")
def sample_raw_video_details() -> List[Dict[str, Any]]:
    return json.loads(SAMPLE_VIDEO_DETAILS_JSON)


#SAMPLE_RAW_VIDEO_DETAILS = json.loads(SAMPLE_VIDEO_DETAILS_JSON)
# Extract the first video ID from the loaded JSON
#SAMPLE_VIDEO_ID = SAMPLE_RAW_VIDEO_DETAILS[0]['id']
SAMPLE_API_TOKEN = "test_api_token"


# Sample data for testing
@pytest.fixture(scope="function")
def yt_transcript_io_client(request, sample_raw_video_details: List[Dict[str, Any]]) -> YtTranscriptIoClient:
    """
    Pytest fixture for YtTranscriptIoClient with function-level scope.
    Mocks the _do_call_yt_transcript_io_api method and ensures it's reset after each test.
    
    Args:
        request: Pytest request fixture for adding finalizers
    
    Returns:
        A YtTranscriptIoClient instance with mocked _do_call_yt_transcript_io_api method
    """
    client = YtTranscriptIoClient(api_token=SAMPLE_API_TOKEN)
    
    # Default mock implementation that can be overridden in individual tests
    when(client)._do_call_yt_transcript_io_api(video_ids=ANY).thenReturn(sample_raw_video_details)
    
    # Add finalizer to unstub after each test
    def fin():
        unstub(client)
    request.addfinalizer(fin)
    
    return client

def test_init(yt_transcript_io_client: YtTranscriptIoClient) -> None:
    assert yt_transcript_io_client.max_video_ids_per_request == 50


def test_call_yt_transcript_io_api(sample_raw_video_details: List[Dict[str, Any]], 
                                  yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_ids: Set[str] = {sample_raw_video_details[0]['id']}
    expected_result: List[Dict[str, Any]] = sample_raw_video_details

    # Call the method
    result: List[Dict[str, Any]] = yt_transcript_io_client.call_yt_transcript_io_api(video_ids=video_ids)

    # Assert the result
    assert result == expected_result

    # Verify that the mocked method was called
    verify(yt_transcript_io_client)._do_call_yt_transcript_io_api(video_ids=video_ids)


def test_call_yt_transcript_io_api_exceeds_max_ids() -> None:
    video_ids: Set[str] = set(str(i) for i in range(51))  # Create more than max allowed IDs
    
    with pytest.raises(ValueError) as excinfo:
        client = YtTranscriptIoClient(api_token=SAMPLE_API_TOKEN)
        client.call_yt_transcript_io_api(video_ids=video_ids)

    assert "Number of video IDs" in str(excinfo.value)


def test_check_video_id_integrity_valid(sample_raw_video_details: List[Dict[str, Any]], 
                                      yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_ids: Set[str] = {sample_raw_video_details[0]['id']}
    video_details: List[VideoDetail] = [VideoDetail.model_validate(sample_raw_video_details[0])]
    assert yt_transcript_io_client.check_video_id_integrity(video_ids, video_details) is True


def test_check_video_id_integrity_missing(sample_raw_video_details: List[Dict[str, Any]], 
                                        yt_transcript_io_client: YtTranscriptIoClient, 
                                        caplog: LogCaptureFixture) -> None:
    video_ids: Set[str] = {sample_raw_video_details[0]['id'], "missing_id"}
    video_details: List[VideoDetail] = [
        VideoDetail.model_validate(sample_raw_video_details[0]),
        VideoDetail.model_validate(sample_raw_video_details[0])
    ]

    with caplog.at_level(logging.WARNING):
        assert yt_transcript_io_client.check_video_id_integrity(video_ids, video_details) is False
        assert "Missing video IDs: {'missing_id'}" in caplog.text


def test_check_video_id_integrity_excess(sample_raw_video_details: List[Dict[str, Any]], 
                                        yt_transcript_io_client: YtTranscriptIoClient, 
                                        caplog: LogCaptureFixture) -> None:
    video_ids: Set[str] = {sample_raw_video_details[0]['id']}
    video_details: List[VideoDetail] = [
        VideoDetail.model_validate(sample_raw_video_details[0]),
        VideoDetail.model_validate(sample_raw_video_details[0])
    ]

    with caplog.at_level(logging.WARNING):
        assert yt_transcript_io_client.check_video_id_integrity(video_ids, video_details) is False
        caplog_match = re.search("Number of video IDs (\\{.+\\}) does not match number of video details (\\[.+\\])", caplog.text)
        assert caplog_match is not None
        assert caplog_match.group(1) == str(video_ids)
        assert caplog_match.group(2) == str(list(map(lambda video_detail: video_detail.id, video_details)))


def test_get_video_details(sample_raw_video_details: List[Dict[str, Any]], 
                          yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_details: List[VideoDetail] = yt_transcript_io_client.get_video_details(sample_raw_video_details)

    assert len(video_details) == 1
    assert isinstance(video_details[0], VideoDetail)
    assert video_details[0].id == sample_raw_video_details[0]['id']
    assert video_details[0].title == "Time Kills Trades: How Adding Maximum Duration Boosts Trading Profits"
    assert len(video_details[0].languages) == 1
    assert video_details[0].languages[0].languageCode == "en"
    assert len(video_details[0].tracks) == 1
    assert video_details[0].tracks[0].language == "English (auto-generated)"
    assert len(sample_raw_video_details[0]['tracks'][0]['transcript']) == len(video_details[0].tracks[0].transcript)
    assert sample_raw_video_details[0]['tracks'][0]['transcript'][0]['text'] == video_details[0].tracks[0].transcript[0].text
    assert sample_raw_video_details[0]['tracks'][0]['transcript'][-1]['text'] == video_details[0].tracks[0].transcript[-1].text
    index = random.randint(0, len(sample_raw_video_details[0]['tracks'][0]['transcript']) - 1)
    assert sample_raw_video_details[0]['tracks'][0]['transcript'][index]['text'] == video_details[0].tracks[0].transcript[index].text

def test_get_joined_transcript_success(sample_raw_video_details: List[Dict[str, Any]],
                                      yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])
    transcript: List[str] = yt_transcript_io_client.get_joined_transcript(video_detail, lang_code="en")

    chunk =  sample_raw_video_details[0]['tracks'][0]['transcript'][0]['text']
    assert transcript[0][0: len(chunk)] == chunk
    chunk =  sample_raw_video_details[0]['tracks'][0]['transcript'][-1]['text']
    assert transcript[0][len(transcript[0]) - len(chunk):] == chunk
    index = random.randint(0, len(sample_raw_video_details[0]['tracks'][0]['transcript']) - 1)
    chunk =  sample_raw_video_details[0]['tracks'][0]['transcript'][index]['text']
    assert chunk in transcript[0]


def test_get_joined_transcript_language_not_found(sample_raw_video_details: List[Dict[str, Any]],
                                                  yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])

    with pytest.raises(ValueError) as excinfo:
        yt_transcript_io_client.get_joined_transcript(video_detail, lang_code="fr")

    assert "Language code 'fr' not found" in str(excinfo.value)


def test_get_joined_transcript_track_not_found(sample_raw_video_details: List[Dict[str, Any]],
                                              yt_transcript_io_client: YtTranscriptIoClient, caplog: LogCaptureFixture) -> None:
    # Modify the video detail to have a different language label
    sample_raw_video_details[0]["tracks"][0]["language"] = "Different Language"
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])

    with caplog.at_level(logging.WARNING):
        transcript: List[str] = yt_transcript_io_client.get_joined_transcript(video_detail, lang_code="en")
        assert transcript == []
        assert "Track for language 'English (auto-generated)' (code: en) not found" in caplog.text


def test_get_joined_transcript_empty(sample_raw_video_details: List[Dict[str, Any]],
                                    yt_transcript_io_client: YtTranscriptIoClient, caplog: LogCaptureFixture) -> None:
    # Modify the video detail to have an empty transcript
    sample_raw_video_details[0]["tracks"][0]["transcript"] = []
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])

    with caplog.at_level(logging.WARNING):
        transcript: List[str] = yt_transcript_io_client.get_joined_transcript(video_detail, lang_code="en")
        assert transcript == []

        caplog_match = re.search('Transcript list is empty for language (.+) for video ID ([^.]+).', caplog.text)
        assert caplog_match is not None
        assert sample_raw_video_details[0]['languages'][0]['label'] in caplog_match.group(1)
        assert ": {0}".format(sample_raw_video_details[0]['languages'][0]['languageCode']) in caplog_match.group(1)
        assert sample_raw_video_details[0]['id'] in caplog_match.group(2)

def test_get_joined_transcript_context_length(sample_raw_video_details: List[Dict[str, Any]],
                                             yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])
    chank_max_length = max(map(lambda item: len(item.text), video_detail.tracks[0].transcript))
    transcript: List[str] = yt_transcript_io_client.get_joined_transcript(
        video_detail=video_detail, lang_code="en", context_length=chank_max_length)
    
    transcript_lengs = list(map(lambda item: len(item), transcript))
    assert max(transcript_lengs) <= chank_max_length
    assert min(transcript_lengs) > 0
    
    assert transcript[0] == sample_raw_video_details[0]["tracks"][0]["transcript"][0]["text"][:len(transcript[0])]
    assert transcript[-1] == sample_raw_video_details[0]["tracks"][0]["transcript"][-1]["text"][len(transcript[-1]) * -1:]


def test_get_joined_transcript_context_length_item_too_long(sample_raw_video_details: List[Dict[str, Any]],
                                                           yt_transcript_io_client: YtTranscriptIoClient) -> None:
    # Modify the video detail to have a long transcript item
    transcription_text = "ThisIsALongWord"
    sample_raw_video_details[0]["tracks"][0]["transcript"][0]["text"] = transcription_text
    video_detail: VideoDetail = VideoDetail.model_validate(sample_raw_video_details[0])

    context_length = 5
    with pytest.raises(RuntimeError) as excinfo:
        yt_transcript_io_client.get_joined_transcript(video_detail, lang_code="en", context_length=context_length)

    assert "Single transcript item length ({0}) exceeds context_length ({1})".format(
        len(transcription_text), context_length) in str(excinfo.value)

@pytest.mark.asyncio
async def test_async_call_yt_transcript_io_api(sample_raw_video_details: List[Dict[str, Any]], 
                                               yt_transcript_io_client: YtTranscriptIoClient) -> None:
    video_ids: Set[str] = {sample_raw_video_details[0]['id']}

    # Call the async method
    result: List[Dict[str, Any]] = await yt_transcript_io_client.async_call_yt_transcript_io_api(video_ids=video_ids)

    # Assert the result
    assert result == sample_raw_video_details

    # Verify that the mocked method was called
    verify(yt_transcript_io_client)._do_call_yt_transcript_io_api(video_ids=video_ids)

