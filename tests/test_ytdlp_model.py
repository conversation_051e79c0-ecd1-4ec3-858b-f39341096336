#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from pydantic import ValidationError

from youtube_recon.model.ytdlp import VideoDataByYtDlp
from youtube_recon.util import create_model_instance_recursive


def test_videodatabyytdlp_with_language():
    """Test that VideoDataByYtDlp accepts a valid language string."""
    data = {
        "categories": ["Science & Technology"],
        "channel": "Test Channel",
        "description": "Test description",
        "duration": 1709,
        "fulltitle": "Test Video",
        "id": "test123",
        "language": "en",
        "tags": ["test", "video"],
        "thumbnail": "https://example.com/thumbnail.jpg",
        "timestamp": 1234567890,
        "uploader": "Test Uploader",
        "uploader_url": "https://example.com/uploader",
        "webpage_url": "https://www.youtube.com/watch?v=test123"
    }
    
    # Direct instantiation
    video_data = VideoDataByYtDlp(**data)
    assert video_data.language == "en"
    
    # Using create_model_instance_recursive
    video_data_recursive = create_model_instance_recursive(VideoDataByYtDlp, data)
    assert video_data_recursive.language == "en"


def test_videodatabyytdlp_with_none_language():
    """Test that VideoDataByYtDlp accepts None for the language field."""
    data = {
        "categories": ["Science & Technology"],
        "channel": "Test Channel",
        "description": "Test description",
        "duration": 1709,
        "fulltitle": "Test Video",
        "id": "test123",
        "language": None,
        "tags": ["test", "video"],
        "thumbnail": "https://example.com/thumbnail.jpg",
        "timestamp": 1234567890,
        "uploader": "Test Uploader",
        "uploader_url": "https://example.com/uploader",
        "webpage_url": "https://www.youtube.com/watch?v=test123"
    }
    
    # Direct instantiation
    video_data = VideoDataByYtDlp(**data)
    assert video_data.language is None
    
    # Using create_model_instance_recursive
    video_data_recursive = create_model_instance_recursive(VideoDataByYtDlp, data)
    assert video_data_recursive.language is None


def test_videodatabyytdlp_without_language():
    """Test that VideoDataByYtDlp works when language field is omitted."""
    data = {
        "categories": ["Science & Technology"],
        "channel": "Test Channel",
        "description": "Test description",
        "duration": 1709,
        "fulltitle": "Test Video",
        "id": "test123",
        "tags": ["test", "video"],
        "thumbnail": "https://example.com/thumbnail.jpg",
        "timestamp": 1234567890,
        "uploader": "Test Uploader",
        "uploader_url": "https://example.com/uploader",
        "webpage_url": "https://www.youtube.com/watch?v=test123"
    }
    
    # Direct instantiation
    video_data = VideoDataByYtDlp(**data)
    assert video_data.language is None
    
    # Using create_model_instance_recursive
    video_data_recursive = create_model_instance_recursive(VideoDataByYtDlp, data)
    assert video_data_recursive.language is None


def test_videodatabyytdlp_with_invalid_language():
    """Test that VideoDataByYtDlp rejects invalid language values."""
    data = {
        "categories": ["Science & Technology"],
        "channel": "Test Channel",
        "description": "Test description",
        "duration": 1709,
        "fulltitle": "Test Video",
        "id": "test123",
        "language": 123,  # Invalid type for language
        "tags": ["test", "video"],
        "thumbnail": "https://example.com/thumbnail.jpg",
        "timestamp": 1234567890,
        "uploader": "Test Uploader",
        "uploader_url": "https://example.com/uploader",
        "webpage_url": "https://www.youtube.com/watch?v=test123"
    }
    
    # Direct instantiation should raise ValidationError
    with pytest.raises(ValidationError) as excinfo:
        VideoDataByYtDlp(**data)
    
    assert "language" in str(excinfo.value)
    assert "string_type" in str(excinfo.value)
    
    # Using create_model_instance_recursive should also raise ValidationError
    with pytest.raises(ValidationError) as excinfo:
        create_model_instance_recursive(VideoDataByYtDlp, data)
    
    assert "language" in str(excinfo.value)
    assert "string_type" in str(excinfo.value)


def test_videodatabyytdlp_missing_required_field():
    """Test that VideoDataByYtDlp rejects data missing required fields."""
    data = {
        "categories": ["Science & Technology"],
        "channel": "Test Channel",
        "description": "Test description",
        # Missing duration field
        "fulltitle": "Test Video",
        "id": "test123",
        "language": None,
        "tags": ["test", "video"],
        "thumbnail": "https://example.com/thumbnail.jpg",
        "timestamp": 1234567890,
        "uploader": "Test Uploader",
        "uploader_url": "https://example.com/uploader",
        "webpage_url": "https://www.youtube.com/watch?v=test123"
    }
    
    # Direct instantiation should raise ValidationError
    with pytest.raises(ValidationError) as excinfo:
        VideoDataByYtDlp(**data)
    
    assert "duration" in str(excinfo.value)
    
    # Using create_model_instance_recursive should also raise ValidationError
    with pytest.raises(Exception) as excinfo:
        create_model_instance_recursive(VideoDataByYtDlp, data)
    
    assert "duration" in str(excinfo.value)
