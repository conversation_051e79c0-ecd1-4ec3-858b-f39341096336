#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify the exclude_file functionality in CLI functions.
"""

import os
import tempfile
from youtube_recon.video_recon_cli import merge_exclude_parameters


def test_merge_exclude_parameters():
    """Test the merge_exclude_parameters helper function"""
    
    print("=== Testing merge_exclude_parameters function ===")
    
    # Test 1: Only exclude parameter (string)
    print("\n1. Testing exclude parameter only (string)")
    result = merge_exclude_parameters(exclude="video1")
    print(f"Input: exclude='video1', exclude_file=None")
    print(f"Result: {result}")
    assert result == ("video1",), f"Expected ('video1',), got {result}"
    print("✓ Passed")
    
    # Test 2: Only exclude parameter (tuple)
    print("\n2. Testing exclude parameter only (tuple)")
    result = merge_exclude_parameters(exclude=("video1", "video2"))
    print(f"Input: exclude=('video1', 'video2'), exclude_file=None")
    print(f"Result: {result}")
    assert set(result) == {"video1", "video2"}, f"Expected video1 and video2, got {result}"
    print("✓ Passed")
    
    # Test 3: Only exclude_file parameter
    print("\n3. Testing exclude_file parameter only")
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("video3\nvideo4\n\nvideo5\n")  # Include empty line
        temp_file = f.name
    
    try:
        result = merge_exclude_parameters(exclude=None, exclude_file=temp_file)
        print(f"Input: exclude=None, exclude_file='{temp_file}'")
        print(f"File contents: video3\\nvideo4\\n\\nvideo5\\n")
        print(f"Result: {result}")
        assert set(result) == {"video3", "video4", "video5"}, f"Expected video3, video4, video5, got {result}"
        print("✓ Passed")
    finally:
        os.unlink(temp_file)
    
    # Test 4: Both exclude and exclude_file parameters
    print("\n4. Testing both exclude and exclude_file parameters")
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("video6\nvideo7\n")
        temp_file = f.name
    
    try:
        result = merge_exclude_parameters(exclude=("video1", "video2"), exclude_file=temp_file)
        print(f"Input: exclude=('video1', 'video2'), exclude_file='{temp_file}'")
        print(f"File contents: video6\\nvideo7\\n")
        print(f"Result: {result}")
        assert set(result) == {"video1", "video2", "video6", "video7"}, f"Expected all 4 videos, got {result}"
        print("✓ Passed")
    finally:
        os.unlink(temp_file)
    
    # Test 5: Duplicate video IDs (should be deduplicated)
    print("\n5. Testing duplicate video IDs")
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("video1\nvideo8\n")  # video1 is also in exclude parameter
        temp_file = f.name
    
    try:
        result = merge_exclude_parameters(exclude="video1", exclude_file=temp_file)
        print(f"Input: exclude='video1', exclude_file='{temp_file}'")
        print(f"File contents: video1\\nvideo8\\n")
        print(f"Result: {result}")
        assert set(result) == {"video1", "video8"}, f"Expected video1 and video8 (deduplicated), got {result}"
        print("✓ Passed")
    finally:
        os.unlink(temp_file)
    
    # Test 6: Neither parameter provided
    print("\n6. Testing neither parameter provided")
    result = merge_exclude_parameters(exclude=None, exclude_file=None)
    print(f"Input: exclude=None, exclude_file=None")
    print(f"Result: {result}")
    assert result is None, f"Expected None, got {result}"
    print("✓ Passed")
    
    # Test 7: Non-existent file (should raise exception)
    print("\n7. Testing non-existent file")
    try:
        result = merge_exclude_parameters(exclude=None, exclude_file="/non/existent/file.txt")
        print("❌ Should have raised an exception")
        assert False, "Should have raised an exception for non-existent file"
    except Exception as e:
        print(f"Input: exclude=None, exclude_file='/non/existent/file.txt'")
        print(f"Exception raised: {type(e).__name__}: {e}")
        print("✓ Passed (correctly raised exception)")
    
    print("\n🎉 All tests passed! merge_exclude_parameters function is working correctly.")


if __name__ == "__main__":
    test_merge_exclude_parameters()
