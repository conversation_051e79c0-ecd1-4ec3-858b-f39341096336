#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from icecream import ic
from confijector.config_loader import config_injector_factory
from injector import Injector
import json
import os
from mockito import ANY, mock, unstub, verify, when
import pytest
from typing import Dict, List, Optional
from urllib3.util import Url, parse_url

from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon
from youtube_recon.model.base_model_enhance import FieldRefBaseModel
from youtube_recon.yt_playlist_recon import get_playlist_meta, get_video_list, get_video_url_list
import youtube_recon.util as util


def test_ytdlp_cli_default_options():
    cli_default_opts = YtdlPlaylistRecon.ytdlp_cli_default_options
    assert isinstance(cli_default_opts, dict)
    assert len(cli_default_opts) > 0
    substantial_cli_default_opts = ic(list(filter(
        lambda k: cli_default_opts[k] is not None and not isinstance(cli_default_opts[k], bool),
        cli_default_opts.keys())))
    assert len(substantial_cli_default_opts) > 0

def test_parse_ytdlp_cli_options():
    cli_opts = ["--no-check-certificates", "--sleep-requests", "5",  "--ignore-errors", "--quiet", "--no-warnings"]
    api_opts = ic(YtdlPlaylistRecon.parse_ytdlp_cli_options(cli_opts=cli_opts))
    assert api_opts.get('nocheckcertificate', None)
    assert api_opts.get('sleep_interval_requests', None) == 5.0
    assert api_opts.get('ignoreerrors', None)
    assert api_opts.get('quiet', None)
    assert api_opts.get('no_warnings', None)

    raise NotImplementedError()

@pytest.fixture(scope='module')
def test_conf_file_fixture():
    project_dir = '.'
    return "{0}{1}{2}{1}{3}{1}{4}{5}{6}".format(
        project_dir, os.path.sep, 'tests', 'resource', '.test.ytdlp.config', os.path.extsep, 'json')

@pytest.fixture(scope='module', autouse=True)
def config_injector_fixture(test_conf_file_fixture: str) -> Injector:
    is_ic_enabled = ic.enabled
    ic.disable()
    injector = config_injector_factory(conf_files=[test_conf_file_fixture])
    if is_ic_enabled:
        ic.enable()
    return injector


# Load the sample data
SAMPLE_JSON_PATH = os.path.join(os.path.dirname(__file__), 'resource', 'ytdlp.playlist.3.sample.json')
with open(SAMPLE_JSON_PATH, 'r') as f:
    SAMPLE_PLAYLIST_DATA = json.load(f)


# Define a simple model for testing
class VideoModel(FieldRefBaseModel):
    id: str
    title: str
    webpage_url: Optional[str] = None


# Helper function to create Url objects
def create_url(url_str: str) -> Url:
    return parse_url(url_str)


@pytest.fixture(scope="function")
def mock_yt_dlp():
    """Mock the yt_dlp.YoutubeDL class"""
    # Create a mock for the YoutubeDL instance
    mock_ydl = mock()
    when(mock_ydl).extract_info(ANY, download=False).thenReturn(SAMPLE_PLAYLIST_DATA)

    # Add __enter__ and __exit__ methods to support context manager
    when(mock_ydl).__enter__().thenReturn(mock_ydl)
    when(mock_ydl).__exit__(ANY, ANY, ANY).thenReturn(None)

    # Mock the YoutubeDL constructor
    when('yt_dlp', strict=False).YoutubeDL(ANY).thenReturn(mock_ydl)

    yield mock_ydl

    # Clean up
    unstub()


@pytest.fixture(scope="function")
def mock_util():
    """Mock the util.create_model_instance_recursive function"""
    # This will be called for each entry in the playlist
    def side_effect(*args, **kwargs):
        # Extract the arguments based on how they're passed
        # If called directly from mockito's thenAnswer
        if len(args) == 1 and hasattr(args[0], 'args') and hasattr(args[0], 'kwargs'):
            invocation = args[0]
            if invocation.args:
                ret_type = invocation.args[0]
                fields_data = invocation.args[1] if len(invocation.args) > 1 else None
            else:
                ret_type = invocation.kwargs.get('ret_type')
                fields_data = invocation.kwargs.get('fields_data')
        # If called with normal arguments
        elif len(args) >= 2:
            ret_type, fields_data = args[0], args[1]
        # If called with keyword arguments
        else:
            ret_type = kwargs.get('ret_type')
            fields_data = kwargs.get('fields_data')

        if fields_data is None:
            # Handle the case of a deleted/unavailable video
            return None

        # Create a simplified model instance for testing
        return VideoModel(
            id=fields_data.get('id', ''),
            title=fields_data.get('title', ''),
            webpage_url=fields_data.get('webpage_url', '')
        )

    when('youtube_recon.util', strict=False).create_model_instance_recursive(
        ret_type=ANY, fields_data=ANY
    ).thenAnswer(side_effect)

    yield

    # Clean up
    unstub()


def test_get_playlist_meta(mock_yt_dlp):
    """Test the get_playlist_meta function"""
    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_playlist_meta(url)

    # Verify the result
    assert result == SAMPLE_PLAYLIST_DATA

    # Verify the mock was called with the correct arguments
    verify(mock_yt_dlp).extract_info(url.url, download=False)


def test_get_video_list(mock_yt_dlp, mock_util):
    """Test the get_video_list function"""
    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_video_list(url, VideoModel)

    # Verify the result
    assert len(result) == len(SAMPLE_PLAYLIST_DATA['entries']) - 1

    # Check that create_model_instance_recursive was called for each entry
    for entry in SAMPLE_PLAYLIST_DATA['entries']:
        if entry is not None:  # Skip None entries (deleted videos)
            verify(util).create_model_instance_recursive(ret_type=VideoModel, fields_data=entry)


def test_get_video_url_list(mock_yt_dlp):
    """Test the get_video_url_list function"""
    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_video_url_list(url)

    # Verify the result
    expected_urls = [entry['webpage_url'] for entry in SAMPLE_PLAYLIST_DATA['entries']
                     if entry is not None and 'webpage_url' in entry]
    assert result == expected_urls


def test_get_video_list_with_deleted_video(mock_yt_dlp, mock_util):
    """Test the get_video_list function with a playlist containing a deleted video"""
    # Modify the sample data to include a None entry (deleted video)
    modified_data = SAMPLE_PLAYLIST_DATA.copy()
    modified_data['entries'] = modified_data['entries'].copy()  # Make a copy of the entries list
    modified_data['entries'][1] = None  # Set the second video to None

    # Update the mock to return the modified data
    when(mock_yt_dlp).extract_info(ANY, download=False).thenReturn(modified_data)

    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_video_list(url, VideoModel)

    # Verify the result - should skip the None entry
    assert len(result) == sum(1 for entry in modified_data['entries'] if entry is not None)

    # Check that create_model_instance_recursive was called only for non-None entries
    for entry in modified_data['entries']:
        if entry is not None:
            verify(util).create_model_instance_recursive(ret_type=VideoModel, fields_data=entry)


def test_get_video_url_list_with_deleted_video(mock_yt_dlp):
    """Test the get_video_url_list function with a playlist containing a deleted video"""
    # Modify the sample data to include a None entry (deleted video)
    modified_data = SAMPLE_PLAYLIST_DATA.copy()
    modified_data['entries'] = modified_data['entries'].copy()  # Make a copy of the entries list
    modified_data['entries'][1] = None  # Set the second video to None

    # Update the mock to return the modified data
    when(mock_yt_dlp).extract_info(ANY, download=False).thenReturn(modified_data)

    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_video_url_list(url)

    # Verify the result - should skip the None entry
    expected_urls = [entry['webpage_url'] for entry in modified_data['entries']
                     if entry is not None and 'webpage_url' in entry]
    assert result == expected_urls


def test_get_video_url_list_with_missing_webpage_url(mock_yt_dlp):
    """Test the get_video_url_list function with a playlist containing entries without webpage_url"""
    # Modify the sample data to include an entry without webpage_url
    modified_data = SAMPLE_PLAYLIST_DATA.copy()
    modified_data['entries'] = modified_data['entries'].copy()  # Make a copy of the entries list
    modified_data['entries'][1] = {'id': 'missing_url', 'title': 'Video without URL'}  # No webpage_url

    # Update the mock to return the modified data
    when(mock_yt_dlp).extract_info(ANY, download=False).thenReturn(modified_data)

    # Create a Url object
    url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Call the function
    result = get_video_url_list(url)

    # Verify the result - should skip the entry without webpage_url
    expected_urls = [entry['webpage_url'] for entry in modified_data['entries']
                     if entry is not None and 'webpage_url' in entry]
    assert result == expected_urls


def test_ytdl_playlist_recon_exclude_single_video(mock_yt_dlp, mock_util):
    """Test YtdlPlaylistRecon excluding a single video by ID (string)"""
    from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon

    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Get the first video ID to exclude
    if original_count > 0:
        exclude_id = result_no_exclude[0].id

        # Test excluding single video
        result_exclude_single = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_id)

        # Verify the excluded video is not in the result
        excluded_ids = [video.id for video in result_exclude_single]
        assert exclude_id not in excluded_ids, f"Excluded video {exclude_id} should not be in result"
        assert len(result_exclude_single) == original_count - 1, "Result should have one less video"


def test_ytdl_playlist_recon_exclude_multiple_videos(mock_yt_dlp, mock_util):
    """Test YtdlPlaylistRecon excluding multiple videos by ID (tuple)"""
    from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon

    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Get the first two video IDs to exclude
    if original_count >= 2:
        exclude_ids = (result_no_exclude[0].id, result_no_exclude[1].id)

        # Test excluding multiple videos
        result_exclude_multiple = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_ids)

        # Verify the excluded videos are not in the result
        excluded_ids = [video.id for video in result_exclude_multiple]
        for excluded_id in exclude_ids:
            assert excluded_id not in excluded_ids, f"Excluded video {excluded_id} should not be in result"
        assert len(result_exclude_multiple) == original_count - 2, "Result should have two less videos"


def test_ytdl_playlist_recon_exclude_nonexistent_video(mock_yt_dlp, mock_util):
    """Test YtdlPlaylistRecon excluding a non-existent video ID"""
    from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon

    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Test excluding non-existent video
    exclude_nonexistent = "non_existent_video_id"
    result_exclude_nonexistent = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_nonexistent)

    # Should return same count as no exclusions since the video doesn't exist
    assert len(result_exclude_nonexistent) == original_count, "Non-existent exclusion should not affect result count"


def test_ytdl_playlist_recon_no_exclude_backward_compatibility(mock_yt_dlp, mock_util):
    """Test that YtdlPlaylistRecon.get_video_list works without exclude parameter (backward compatibility)"""
    from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon
    from youtube_recon.model.ytdlp import VideoDataByYtDlp

    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Test without exclude parameter
    result = playlist_recon.get_video_list(playlist_url=test_url)

    # Should return all non-None entries from the sample data
    expected_count = sum(1 for entry in SAMPLE_PLAYLIST_DATA['entries'] if entry is not None)
    assert len(result) == expected_count, f"Expected {expected_count} videos, got {len(result)}"

    # All results should be VideoDataByYtDlp instances
    for video in result:
        assert isinstance(video, VideoDataByYtDlp), "All results should be VideoDataByYtDlp instances"
