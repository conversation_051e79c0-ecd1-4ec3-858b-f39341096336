#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from pydantic import BaseModel, ValidationError
import pytest
from typing import List, Dict, Optional
from youtube_recon import util


class NestedModel(BaseModel):
    nested_id: int
    nested_name: str
    optional_nested: Optional[str] = None

class ItemModel(BaseModel):
    item_id: int
    item_value: str

class ComplexModel(BaseModel):
    id: int
    name: str
    nested: NestedModel                 # Direct nested model
    optional_nested: Optional[NestedModel] = None # Optional nested model with default None
    items_list: List[ItemModel]         # List of models
    items_dict: Dict[str, ItemModel]    # Dict mapping to models
    simple_list: List[int]              # List of primitives
    optional_field: Optional[str] = "default" # Optional primitive with default


# Example JSON data string
json_data_full = """
{
  "id": 123,
  "name": "Main Model Instance",
  "nested": {
    "nested_id": 456,
    "nested_name": "Nested Instance",
    "optional_nested": "I am nested optionally"
  },
  "optional_nested": null,
  "items_list": [
    {"item_id": 1, "item_value": "Value 1"},
    {"item_id": 2, "item_value": "Value 2"}
  ],
  "items_dict": {
    "key1": {"item_id": 10, "item_value": "Dict Value 1"},
    "key2": {"item_id": 20, "item_value": "Dict Value 2"}
  },
  "simple_list": [100, 200, 300],
  "optional_field": "I have a value"
}
"""

def test_full_instantiation():
    # Full Instantiation Example
    full_instance = util.create_model_instance_recursive(ComplexModel, json_data_full)
    print(full_instance.model_dump_json(indent=2))
    # Verification checks
    assert isinstance(full_instance.nested, NestedModel)
    assert full_instance.nested.optional_nested == "I am nested optionally"
    assert full_instance.optional_nested is None
    assert isinstance(full_instance.items_list[0], ItemModel)
    assert isinstance(full_instance.items_dict["key1"], ItemModel)
    assert full_instance.optional_field == "I have a value"


# Example JSON for required_only test
json_data_req = """
{
  "id": 789,
  "name": "Required Only Test",
  "nested": {
    "nested_id": 101,
    "nested_name": "Nested Required"
  },
  "items_list": [
    {"item_id": 3, "item_value": "Req List Val"}
  ],
  "items_dict": {
    "req_key": {"item_id": 30, "item_value": "Req Dict Val"}
  },
  "simple_list": [999]
}
"""

def test_required_only_instantiation():
    # Required Only Instantiation Example
    required_instance = util.create_model_instance_recursive(ComplexModel, json_data_req, required_only=True)
    print(required_instance.model_dump_json(indent=2))
    # Verification checks
    assert required_instance.id == 789
    assert required_instance.name == "Required Only Test"
    assert isinstance(required_instance.nested, NestedModel)
    assert required_instance.nested.nested_id == 101
    # Check optional fields received defaults or None, not values from json_data_req (unless they were required)
    assert getattr(required_instance.nested, 'optional_nested', None) is None # Not required, should be None
    assert required_instance.optional_nested is None # Not required, should be None
    assert required_instance.optional_field == "default" # Not required, should get model default
    assert isinstance(required_instance.items_list[0], ItemModel)
    assert isinstance(required_instance.items_dict["req_key"], ItemModel)


# Example: Missing required field in nested model (required_only=True)
json_missing_nested_req = """
{
  "id": 999,
  "name": "Test Missing Required",
  "nested": {
    "nested_id": 789
  },
  "items_list": [], "items_dict": {}, "simple_list": []
}
"""

def test_missing_required_nested():
    # Testing Missing Required Nested Field (required_only=True)
    with pytest.raises(KeyError) as excpt_info:
        util.create_model_instance_recursive(ComplexModel, json_missing_nested_req, required_only=True)
    
    assert 'nested_name' in excpt_info.value.args[0] and 'NestedModel' in excpt_info.value.args[0]

def test_invalid_json():
    # Example: Invalid JSON string
    invalid_json = '{"id": 1, "name": "Test", "nested": {'
    with pytest.raises(ValueError) as excpt_info:
        util.create_model_instance_recursive(ComplexModel, invalid_json)
    
    assert 'Invalid JSON' in excpt_info.value.args[0]

def test_type_mismatch():
    # Example: Type mismatch handled by Pydantic
    json_type_mismatch = """
    {
        "id": "not-an-int", "name": "Mismatch", "nested": {"nested_id": 1, "nested_name": "N"},
        "items_list": [], "items_dict": {}, "simple_list": []
    }
    """
    with pytest.raises(ValidationError) as excpt_info:
        util.create_model_instance_recursive(ComplexModel, json_type_mismatch)
    
    assert 'id' == excpt_info.value.errors()[0]['loc'][0]
    assert json.loads(json_type_mismatch)['id'] == excpt_info.value.errors()[0]['input']
    assert 'int_parsing' == excpt_info.value.errors()[0]['type']
