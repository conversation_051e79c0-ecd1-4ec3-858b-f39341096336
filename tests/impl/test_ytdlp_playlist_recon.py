#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from confijector.config_loader import Config<PERSON>oader, config_bind_factory
from confijector.config_parser import ConfigParser, ConfigParserModule
from injector import Injector
import json
import logging
from mockito import mock, when, verify, verifyNoMoreInteractions, unstub
from mockito import matchers
import os
from typing import List, Optional, Tuple, Union
from urllib3.util import parse_url, Url
import yt_dlp
from youtube_recon.impl.ytdlp_playlist_recon import add_ytdl_playlist_recon_provider, YtdlPlaylistRecon, YtdlLogger
from youtube_recon.model.ytdlp import VideoDataByYtDlp
import youtube_recon.util as util


@pytest.fixture(scope='module')
def test_conf_file_fixture() -> str:
    return os.path.join(os.path.curdir, 'tests', 'resource', '.test.ytdlp.config.json')


@pytest.fixture(scope='module', autouse=True)
def config_injector_fixture(test_conf_file_fixture: str) -> Injector:
    injector = Injector(modules=[
        config_bind_factory(conf_files=[test_conf_file_fixture]),
        ConfigParserModule()
    ])
    return injector


def test_gen_ytdl_provider(test_conf_file_fixture: str, config_injector_fixture: Injector):
    injector_obj = add_ytdl_playlist_recon_provider(injct=config_injector_fixture,
                                                    conf_ytdlp_cli_opts_key_seq=('youtube_recon', 'cli', 'ytdlp', 'cli_opts'),
                                                    ytdl_log_level=logging.DEBUG)
    ydl = injector_obj.get(yt_dlp.YoutubeDL)
    assert isinstance(ydl, yt_dlp.YoutubeDL)

    ytdlp_cli_opts = None
    with open(test_conf_file_fixture, 'rt') as fd:
        ytdlp_cli_opts = json.load(fd)['youtube_recon']['cli']['ytdlp']['cli_opts']
    assert isinstance(ydl.params, dict) and len(ydl.params) > len(ytdlp_cli_opts)
    assert isinstance(ydl.params['logger'], YtdlLogger)
    cli_opt = '--no-check-certificates'
    assert cli_opt in ytdlp_cli_opts and ydl.params['nocheckcertificate']
    cli_opt = '--sleep-requests'
    assert cli_opt in ytdlp_cli_opts, (
        "Test Error: {0} not found in ytdlp's cli_opts item of {1} file".format(cli_opt, test_conf_file_fixture))
    assert ydl.params['sleep_interval_requests'] == int(ytdlp_cli_opts[ytdlp_cli_opts.index(cli_opt)+1])
    cli_opt = '--ignore-errors'
    assert cli_opt in ytdlp_cli_opts and ydl.params['ignoreerrors']
    cli_opt = '--quiet'
    assert cli_opt in ytdlp_cli_opts and ydl.params['quiet']
    cli_opt = '--no-warnings'
    assert cli_opt in ytdlp_cli_opts and ydl.params['no_warnings']


# Load the sample data for exclude tests
SAMPLE_JSON_PATH = os.path.join(os.path.dirname(__file__), '..', 'resource', 'ytdlp.playlist.3.sample.json')
with open(SAMPLE_JSON_PATH, 'r') as f:
    SAMPLE_PLAYLIST_DATA = json.load(f)


def create_url(url_str: str) -> Url:
    """Helper function to create Url objects"""
    return parse_url(url_str)


@pytest.fixture(scope="function")
def mock_yt_dlp_for_exclude():
    """Mock the yt_dlp.YoutubeDL class for exclude tests"""
    # Create a mock for the YoutubeDL instance
    mock_ydl = mock()
    when(mock_ydl).extract_info(matchers.any(), download=False).thenReturn(SAMPLE_PLAYLIST_DATA)
    when(mock_ydl).get_playlist_meta(playlist_url=matchers.any()).thenReturn(SAMPLE_PLAYLIST_DATA)

    yield mock_ydl

    # Clean up
    unstub()


@pytest.fixture(scope="function")
def mock_util_for_exclude():
    """Mock the util.create_model_instance_recursive function for exclude tests"""
    def side_effect(ret_type, fields_data):
        if fields_data is None:
            return None

        # Create a simplified VideoDataByYtDlp instance for testing
        return VideoDataByYtDlp(
            categories=fields_data.get('categories', []),
            channel=fields_data.get('channel', ''),
            description=fields_data.get('description', ''),
            duration=fields_data.get('duration', 0),
            fulltitle=fields_data.get('fulltitle', ''),
            id=fields_data.get('id', ''),
            tags=fields_data.get('tags', []),
            thumbnail=fields_data.get('thumbnail', ''),
            timestamp=fields_data.get('timestamp', 0),
            uploader=fields_data.get('uploader', ''),
            uploader_url=fields_data.get('uploader_url', ''),
            webpage_url=fields_data.get('webpage_url', '')
        )

    when(util).create_model_instance_recursive(
        ret_type=matchers.any(), fields_data=matchers.any()
    ).thenAnswer(side_effect)

    yield

    # Clean up
    unstub()


def test_get_video_list_exclude_single_video(mock_yt_dlp_for_exclude, mock_util_for_exclude):
    """Test excluding a single video by ID (string)"""
    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp_for_exclude)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Get the first video ID to exclude
    if original_count > 0:
        exclude_id = result_no_exclude[0].id

        # Test excluding single video
        result_exclude_single = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_id)

        # Verify the excluded video is not in the result
        excluded_ids = [video.id for video in result_exclude_single]
        assert exclude_id not in excluded_ids, f"Excluded video {exclude_id} should not be in result"
        assert len(result_exclude_single) == original_count - 1, "Result should have one less video"


def test_get_video_list_exclude_multiple_videos(mock_yt_dlp_for_exclude, mock_util_for_exclude):
    """Test excluding multiple videos by ID (tuple)"""
    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp_for_exclude)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Get the first two video IDs to exclude
    if original_count >= 2:
        exclude_ids = (result_no_exclude[0].id, result_no_exclude[1].id)

        # Test excluding multiple videos
        result_exclude_multiple = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_ids)

        # Verify the excluded videos are not in the result
        excluded_ids = [video.id for video in result_exclude_multiple]
        for excluded_id in exclude_ids:
            assert excluded_id not in excluded_ids, f"Excluded video {excluded_id} should not be in result"
        assert len(result_exclude_multiple) == original_count - 2, "Result should have two less videos"


def test_get_video_list_exclude_nonexistent_video(mock_yt_dlp_for_exclude, mock_util_for_exclude):
    """Test excluding a non-existent video ID"""
    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp_for_exclude)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Get all videos first
    result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
    original_count = len(result_no_exclude)

    # Test excluding non-existent video
    exclude_nonexistent = "non_existent_video_id"
    result_exclude_nonexistent = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_nonexistent)

    # Should return same count as no exclusions since the video doesn't exist
    assert len(result_exclude_nonexistent) == original_count, "Non-existent exclusion should not affect result count"


def test_get_video_list_no_exclude(mock_yt_dlp_for_exclude, mock_util_for_exclude):
    """Test that get_video_list works without exclude parameter (backward compatibility)"""
    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_yt_dlp_for_exclude)

    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")

    # Test without exclude parameter
    result = playlist_recon.get_video_list(playlist_url=test_url)

    # Should return all non-None entries from the sample data
    expected_count = sum(1 for entry in SAMPLE_PLAYLIST_DATA['entries'] if entry is not None)
    assert len(result) == expected_count, f"Expected {expected_count} videos, got {len(result)}"

    # All results should be VideoDataByYtDlp instances
    for video in result:
        assert isinstance(video, VideoDataByYtDlp), "All results should be VideoDataByYtDlp instances"


