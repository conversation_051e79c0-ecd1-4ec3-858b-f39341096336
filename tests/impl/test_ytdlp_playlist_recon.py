#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from confijector.config_loader import Config<PERSON>oader, config_bind_factory
from confijector.config_parser import ConfigParser, ConfigParserModule
from injector import Injector
import json
import logging
from mockito import mock, when, verify, verifyNoMoreInteractions, unstub
from mockito import matchers
import os
import yt_dlp
from youtube_recon.impl.ytdlp_playlist_recon import add_ytdl_playlist_recon_provider, YtdlPlaylistRecon, YtdlLogger


@pytest.fixture(scope='module')
def test_conf_file_fixture() -> str:
    return os.path.join(os.path.curdir, 'tests', 'resource', '.test.ytdlp.config.json')


@pytest.fixture(scope='module', autouse=True)
def config_injector_fixture(test_conf_file_fixture: str) -> Injector:
    injector = Injector(modules=[
        config_bind_factory(conf_files=[test_conf_file_fixture]),
        ConfigParserModule()
    ])
    return injector


def test_gen_ytdl_provider(test_conf_file_fixture: str, config_injector_fixture: Injector):
    injector_obj = add_ytdl_playlist_recon_provider(injct=config_injector_fixture,
                                                    conf_ytdlp_cli_opts_key_seq=('youtube_recon', 'cli', 'ytdlp', 'cli_opts'),
                                                    ytdl_log_level=logging.DEBUG)
    ydl = injector_obj.get(yt_dlp.YoutubeDL)
    assert isinstance(ydl, yt_dlp.YoutubeDL)

    ytdlp_cli_opts = None
    with open(test_conf_file_fixture, 'rt') as fd:
        ytdlp_cli_opts = json.load(fd)['youtube_recon']['cli']['ytdlp']['cli_opts']
    assert isinstance(ydl.params, dict) and len(ydl.params) > len(ytdlp_cli_opts)
    assert isinstance(ydl.params['logger'], YtdlLogger)
    cli_opt = '--no-check-certificates'
    assert cli_opt in ytdlp_cli_opts and ydl.params['nocheckcertificate']
    cli_opt = '--sleep-requests'
    assert cli_opt in ytdlp_cli_opts, (
        "Test Error: {0} not found in ytdlp's cli_opts item of {1} file".format(cli_opt, test_conf_file_fixture))
    assert ydl.params['sleep_interval_requests'] == int(ytdlp_cli_opts[ytdlp_cli_opts.index(cli_opt)+1])
    cli_opt = '--ignore-errors'
    assert cli_opt in ytdlp_cli_opts and ydl.params['ignoreerrors']
    cli_opt = '--quiet'
    assert cli_opt in ytdlp_cli_opts and ydl.params['quiet']
    cli_opt = '--no-warnings'
    assert cli_opt in ytdlp_cli_opts and ydl.params['no_warnings']


