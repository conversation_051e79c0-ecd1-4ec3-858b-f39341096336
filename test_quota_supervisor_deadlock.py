#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Focused test to reproduce and verify the fix for the QuotaSupervisor deadlock issue.
This test specifically targets the race condition in async_consume().
"""

import asyncio
import logging
import time
import threading
from typing import List
import random

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import both versions for comparison
import sys
import os
# Add the src directory to path to import our fixed version
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.dirname(__file__))
from quota_supervisor_fixed import QuotaSupervisor as FixedQuotaSupervisor

# Also import the original version for comparison
sys.path.insert(0, './.venv/lib/python3.10/site-packages')
try:
    from quota_supervisor.quota_supervisor import QuotaSuper<PERSON> as OriginalQuotaSupervisor
    ORIGINAL_AVAILABLE = True
except ImportError:
    logger.warning("Original QuotaSupervisor not available - will only test fixed version")
    ORIGINAL_AVAILABLE = False


async def simulate_hanging_api_call(call_id: int, hang_duration: float = 5.0, hang_probability: float = 0.05):
    """Simulate an API call that might hang"""
    if random.random() < hang_probability:
        logger.warning(f"API call {call_id} hanging for {hang_duration}s")
        await asyncio.sleep(hang_duration)
        raise asyncio.TimeoutError(f"API call {call_id} timed out")

    # Normal processing - much shorter
    processing_time = random.uniform(0.1, 0.5)
    await asyncio.sleep(processing_time)
    return f"Result from call {call_id}"


async def worker_task(task_id: int, quota_supervisor, total_calls: int = 5):
    """Worker task that makes multiple API calls using the quota supervisor"""
    logger.info(f"Worker {task_id} starting with {total_calls} calls")
    results = []
    
    for call_num in range(total_calls):
        call_id = f"{task_id}-{call_num}"
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.debug(f"Worker {task_id} call {call_num}: Got quota")
                result = await simulate_hanging_api_call(
                    call_id=call_id,
                    hang_duration=3.0,  # Much shorter hang for testing
                    hang_probability=0.05  # 5% chance of hanging
                )
                logger.debug(f"Worker {task_id} call {call_num}: Completed")
                results.append(result)
        except Exception as e:
            logger.error(f"Worker {task_id} call {call_num}: Failed with {e}")
            results.append(f"Error: {e}")
    
    logger.info(f"Worker {task_id} completed with {len(results)} results")
    return results


async def test_quota_supervisor_deadlock(quota_supervisor_class, test_name: str, num_workers: int = 10):
    """Test for deadlock with multiple concurrent workers"""
    logger.info(f"=== {test_name} ===")
    
    # Create quota supervisor
    quota_supervisor = quota_supervisor_class(
        max_cap=30,
        time_frame=60
    )
    
    # Create worker tasks
    tasks = []
    for i in range(num_workers):
        task = asyncio.create_task(worker_task(i, quota_supervisor, total_calls=3))
        tasks.append(task)
    
    start_time = time.time()
    
    try:
        # Set a reasonable timeout for the entire test
        test_timeout = 30.0  # 30 seconds should be enough for normal operation
        
        logger.info(f"Starting {num_workers} workers with {test_timeout}s timeout")
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=test_timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Analyze results
        successful_workers = sum(1 for r in results if isinstance(r, list))
        failed_workers = sum(1 for r in results if isinstance(r, Exception))
        
        logger.info(f"{test_name} completed in {duration:.2f}s")
        logger.info(f"Workers: {successful_workers} successful, {failed_workers} failed")
        
        # Check for deadlock indicators
        if duration > test_timeout * 0.9:  # If we used most of the timeout
            logger.warning(f"⚠️  {test_name} took {duration:.2f}s - possible deadlock")
            return False, duration, successful_workers, failed_workers
        else:
            logger.info(f"✅ {test_name} completed normally in {duration:.2f}s")
            return True, duration, successful_workers, failed_workers
            
    except asyncio.TimeoutError:
        end_time = time.time()
        duration = end_time - start_time
        logger.error(f"❌ {test_name} TIMED OUT after {duration:.2f}s - DEADLOCK DETECTED!")
        
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        
        return False, duration, 0, num_workers
    
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        logger.error(f"❌ {test_name} failed with exception: {e}")
        return False, duration, 0, num_workers


async def test_mixed_sync_async_operations(quota_supervisor):
    """Test that sync and async operations work together correctly"""
    logger.info("=== Testing Mixed Sync/Async Operations ===")
    
    results = []
    
    def sync_worker():
        """Synchronous worker that uses the quota supervisor"""
        try:
            with quota_supervisor.consume(rate_limit=1):
                logger.debug("Sync worker: Got quota")
                time.sleep(1)  # Simulate work
                logger.debug("Sync worker: Completed")
                return "sync_success"
        except Exception as e:
            logger.error(f"Sync worker failed: {e}")
            return f"sync_error: {e}"
    
    async def async_worker():
        """Asynchronous worker that uses the quota supervisor"""
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.debug("Async worker: Got quota")
                await asyncio.sleep(1)  # Simulate async work
                logger.debug("Async worker: Completed")
                return "async_success"
        except Exception as e:
            logger.error(f"Async worker failed: {e}")
            return f"async_error: {e}"
    
    # Run sync operation in thread and async operation concurrently
    sync_task = asyncio.create_task(asyncio.to_thread(sync_worker))
    async_task = asyncio.create_task(async_worker())
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(sync_task, async_task, return_exceptions=True),
            timeout=30.0
        )
        
        logger.info(f"Mixed operations results: {results}")
        
        # Check if both completed successfully
        success = all("success" in str(r) for r in results)
        return success
        
    except asyncio.TimeoutError:
        logger.error("Mixed operations test timed out - possible deadlock")
        return False


async def main():
    """Run all deadlock tests"""
    logger.info("Starting QuotaSupervisor deadlock tests")
    
    test_results = []
    
    # Test 1: Fixed QuotaSupervisor
    logger.info("\n" + "="*50)
    success, duration, successful, failed = await test_quota_supervisor_deadlock(
        FixedQuotaSupervisor, 
        "Fixed QuotaSupervisor Test",
        num_workers=15
    )
    test_results.append(("Fixed QuotaSupervisor", success, duration))
    
    # Test 2: Original QuotaSupervisor (if available)
    if ORIGINAL_AVAILABLE:
        logger.info("\n" + "="*50)
        success, duration, successful, failed = await test_quota_supervisor_deadlock(
            OriginalQuotaSupervisor,
            "Original QuotaSupervisor Test", 
            num_workers=15
        )
        test_results.append(("Original QuotaSupervisor", success, duration))
    
    # Test 3: Mixed sync/async operations with fixed version
    logger.info("\n" + "="*50)
    fixed_supervisor = FixedQuotaSupervisor(max_cap=10, time_frame=60, lock_timeout=10.0)
    mixed_success = await test_mixed_sync_async_operations(fixed_supervisor)
    test_results.append(("Mixed Sync/Async", mixed_success, 0))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    all_passed = True
    for test_name, success, duration in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        logger.info(f"{test_name}: {status}{duration_str}")
        if not success:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 ALL TESTS PASSED! The deadlock fix appears to be working correctly.")
        logger.info("You can now proceed to test with your YouTube playlist.")
    else:
        logger.warning("\n⚠️  Some tests failed. Please review the results above.")
    
    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Tests failed with exception: {e}")
        exit(1)
