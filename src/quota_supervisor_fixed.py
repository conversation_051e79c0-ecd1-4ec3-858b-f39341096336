#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
from contextlib import asynccontextmanager, contextmanager
import contextvars  # For per-task state
from functools import partial
from icecream import ic
from logear.autolog_exception import adapt_autolog_exception
import logging
import time
import threading
from typing import Any, cast, Optional, Type  # For type hints


class SharedLockTimeoutError(Exception):  # Placeholder with basic hint
    """Custom exception for SharedLock timeout to distinguish from general asyncio.TimeoutError"""

    def __init__(self, message: str = "Lock acquisition timed out") -> None:
        super().__init__(message)


class SharedLockTaskError(RuntimeError):  # New: For general task failures (e.g., exceptions in futures)
    def __init__(self, message: str, unique_key: Optional[str] = None, original_exc: Optional[Exception] = None) -> None:
        self.unique_key = unique_key
        self.original_exc = original_exc  # Wrap the root cause for chaining
        full_message = f"{message} (key: {unique_key})" if unique_key else message
        if original_exc:
            full_message += f"\nOriginal error: {type(original_exc).__name__}: {str(original_exc)}"
        super().__init__(full_message)


# Module-level ContextVar for unique_key in async paths
unique_key_var: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('unique_key', default=None)


class SharedLock:
    def __init__(self, timeout: Optional[float] = None) -> None:
        self._lock: threading.Lock = threading.Lock()
        self.timeout: Optional[float] = timeout
        self.acquired_states: set[str] = set()  # Tracks unique_keys of successful acquisitions (optional for sync)
        self._local: threading.local = threading.local()  # Optional: Per-thread set for active keys (async-only)

    def __enter__(self) -> 'SharedLock':
        acquired: bool = self._lock.acquire(timeout=self.timeout or -1)
        if not acquired:
            raise SharedLockTimeoutError("Timeout occurred during lock acquisition")

        return self

    def __exit__(
            self,
            exc_type: Optional[Type[BaseException]],
            exc_value: Optional[BaseException],
            traceback: Optional[Any]
    ) -> Optional[bool]:
        # Sync: No key-based cleanup needed
        self._lock.release()
        ic("lock released".format(" due to {0}".format(exc_value) if exc_value else ""))
        return None  # Or True if suppressing exceptions

    def _awrap_enter(self, unique_key: str) -> 'SharedLock':
        if not unique_key:
            raise ValueError("unique_key is required for async lock acquisition")  # Fail-fast safeguard

        # Async case: Setup thread-local
        if not hasattr(self._local, 'active_keys'):
            self._local.active_keys = set[str]()

        self.__enter__()
        self._local.active_keys.add(unique_key)
        self.acquired_states.add(unique_key)

        ic("lock acquired for key {0}".format(unique_key))

        return self

    def _cleanup_callback(self, unique_key: str, future: asyncio.Future[Any]) -> None:
        ic("future for key {0}: {1}".format(unique_key, future))

        if future is None:
            ic(f"Warning: Cleanup callback invoked with None future for key {unique_key}")
            return  # Guard against None future
            
        if not future.cancelled() and future.exception() is None:
            return

        if future.cancelled():
            ic("Warning: Async enter for key {0} was cancelled.".format(unique_key))  # Or use logging

        if unique_key in self.acquired_states:
            def release_in_thread() -> None:
                ic("Releasing lock for {0} in callback.".format(unique_key))
                self._lock.release()
                self.acquired_states.discard(unique_key)
                if hasattr(self._local, 'active_keys'):
                    self._local.active_keys.discard(unique_key)
                    if not self._local.active_keys:
                        del self._local.active_keys

            asyncio.get_running_loop().run_in_executor(None, release_in_thread)

        # After cleanup, raise if it was an exception (to propagate failure)
        if future.exception() is not None:
            raise SharedLockTaskError("Task failed during async enter", unique_key, future.exception())


    async def __aenter__(self) -> 'SharedLock':
        loop: asyncio.AbstractEventLoop = asyncio.get_running_loop()
        task_id: int = id(asyncio.current_task())
        timestamp: str = str(time.monotonic_ns())
        unique_key: str = "{0}_{1}".format(task_id, timestamp)

        unique_key_var.set(unique_key)

        partial_cleanup = partial(self._cleanup_callback, unique_key)

        future: asyncio.Future[Any] = loop.run_in_executor(None, partial(self._awrap_enter, unique_key))
        future.add_done_callback(partial_cleanup)

        try:
            await asyncio.wait_for(future, timeout=self.timeout)
            return await future
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            future.cancel()
            raise SharedLockTimeoutError("Timeout or cancellation during async acquisition") from e
        except Exception as e:  # Catch general task errors (e.g., propagated from callback)
            raise SharedLockTaskError("Unexpected task error during enter", unique_key, e) from e

    def _awrap_exit(
            self,
            exc_type: Optional[Type[BaseException]],
            exc_value: Optional[BaseException],
            traceback: Optional[Any],
            unique_key: str
    ) -> Optional[bool]:
        if not unique_key:
            raise ValueError("unique_key is required for async lock release")  # Symmetric safeguard

        # Combined lock release and cleanup (symmetric to _awrap_enter)
        suppress: Optional[bool] = self.__exit__(exc_type, exc_value, traceback)

        self.acquired_states.discard(unique_key)

        if hasattr(self._local, 'active_keys'):
            self._local.active_keys.discard(unique_key)
            if not self._local.active_keys:
                del self._local.active_keys

        return suppress

    def _exit_cleanup_callback(self, unique_key: str, future: asyncio.Future[Optional[bool]]) -> None:
        ic("future for key {0}: {1}".format(unique_key, future))

        if future is None:
            ic(f"Warning: Exit cleanup callback invoked with None future for key {unique_key}")
            return  # Guard against None future

        if not future.cancelled() and future.exception() is None:
            return

        # Symmetric to _cleanup_callback: Handle exit failures (e.g., log if errored)
        if future.cancelled():
            # Optional: Add logging or recovery; kept minimal for symmetry
            ic("Warning: Async exit for key {0} was cancelled.".format(unique_key))  # Or use logging

        # For symmetry/consistency, add idempotent cleanup if state indicates
        if unique_key in self.acquired_states:
            def release_in_thread() -> None:
                self._lock.release()
                self.acquired_states.discard(unique_key)
                if hasattr(self._local, 'active_keys'):
                    self._local.active_keys.discard(unique_key)
                    if not self._local.active_keys:
                        del self._local.active_keys
            
            asyncio.get_running_loop().run_in_executor(None, release_in_thread)

        # After any cleanup, raise if it was an exception (to propagate failure)
        if future.exception() is not None:
            raise SharedLockTaskError("Task failed during async exit", unique_key, future.exception())

    async def __aexit__(
            self,
            exc_type: Optional[Type[BaseException]],
            exc_value: Optional[BaseException],
            traceback: Optional[Any]
    ) -> Optional[bool]:
        unique_key: Optional[str] = unique_key_var.get()
        if unique_key is None:
            ic("No lock to release since unique_key is None.")
            return None

        # Symmetric to enter: Create a partial for combined exit/cleanup
        partial_exit = partial(self._awrap_exit, exc_type, exc_value, traceback, unique_key)

        # Symmetric: Add a done callback for handling exit failures (e.g., log or extra cleanup)
        partial_exit_cleanup = partial(self._exit_cleanup_callback, unique_key)

        loop: asyncio.AbstractEventLoop = asyncio.get_running_loop()
        future: asyncio.Future[Optional[bool]] = loop.run_in_executor(None, partial_exit)
        future.add_done_callback(partial_exit_cleanup)

        try:
            # Symmetric await (no timeout needed for exit, but could add if desired)
            suppress_result: Optional[bool] = await future
            unique_key_var.set(None)  # Reset at end (reverse of set in enter)
            return suppress_result
        except (asyncio.TimeoutError, asyncio.CancelledError) as e:
            future.cancel()
            raise SharedLockTimeoutError("Timeout or cancellation during async exit") from e
        except Exception as e:  # Catch general task errors
            raise SharedLockTaskError("Unexpected task error during exit", unique_key, e) from e


class QuotaSupervisorLockTimeoutError(Exception):
    """Custom exception for QuotaSupervisor lock timeout"""
    pass


@adapt_autolog_exception()
class QuotaSupervisor:
    """
    A rate limiting utility class implemented as a singleton to control the frequency of operations
    within a given time frame. It ensures that operations do not exceed a specified rate,
    making it useful for scenarios like API rate limiting or controlling resource access rates.

    This version uses the SharedLock design to properly coordinate between sync and async operations
    while supporting timeout functionality.
    """

    def __init__(self, max_cap: int, time_frame: Optional[float] = 1, lock_timeout: Optional[float] = 30.0):
        """
        Parameters:
            max_cap (int): The total quotas for operations allowed within the time frame.
            time_frame (Optional[int]): The duration of the time frame in seconds.
            lock_timeout (Optional[float]): Maximum time to wait for lock acquisition in seconds.
        """
        self.max_cap = max_cap
        self.time_frame = time_frame
        self.remaining_quota = max_cap
        self.timestamp = None
        self.lock_timeout = lock_timeout

        # Use the SharedLock design for proper sync/async coordination
        self.shared_lock = SharedLock(timeout=lock_timeout)

    def _wait_and_reset(self):
        """Synchronous method to wait and reset the quota."""
        wait_time = max(0, self.time_frame - (time.time() - self.timestamp))
        if wait_time > 0:
            ic(f"Waiting {wait_time:.2f}s for quota reset")
            time.sleep(wait_time)
        self.remaining_quota = self.max_cap
        self.timestamp = None
        ic("Quota reset completed")

    async def _async_wait_and_reset(self):
        """Asynchronous method to wait and reset the quota."""
        wait_time = max(0, self.time_frame - (time.time() - self.timestamp))
        if wait_time > 0:
            ic(f"Async waiting {wait_time:.2f}s for quota reset")
            await asyncio.sleep(wait_time)
        self.remaining_quota = self.max_cap
        self.timestamp = None
        ic("Async quota reset completed")

    @asynccontextmanager
    async def async_consume(self, rate_limit):
        """
        Consumes a quota asynchronously using the SharedLock design.
        If the quota is available, it is immediately consumed.
        If the quota is not available, this method waits until it becomes available.

        Returns:
            None
        """
        try:
            async with self.shared_lock:
                ic("Acquired shared lock object {0}.".format(self.shared_lock._lock))
                try:
                    if self.timestamp is None:
                        self.timestamp = time.time()

                    if ic(self.remaining_quota >= rate_limit):
                        self.remaining_quota -= rate_limit
                    else:
                        ic("Quota exhausted, waiting for reset")
                        await self._async_wait_and_reset()
                        self.remaining_quota -= rate_limit

                    ic(self.remaining_quota)
                    yield self
                finally:
                    ic("Releasing shared lock object {0}.".format(self.shared_lock._lock))
                    # Lock is automatically released by async with
        except SharedLockTimeoutError as e:
            ic(f"Lock acquisition timeout: {e}")
            raise QuotaSupervisorLockTimeoutError(str(e))

    @contextmanager
    def consume(self, rate_limit):
        """
        Consumes a quota synchronously using the SharedLock design.
        If the quota is available, it is immediately consumed.
        If the quota is not available, this method waits until it becomes available.

        Returns:
            None
        """
        try:
            with self.shared_lock:
                ic("Acquired shared lock object {0}.".format(self.shared_lock._lock))
                try:
                    if self.timestamp is None:
                        self.timestamp = time.time()

                    if self.remaining_quota >= rate_limit:
                        self.remaining_quota -= rate_limit
                    else:
                        ic("Quota exhausted, waiting for reset")
                        self._wait_and_reset()
                        self.remaining_quota -= rate_limit

                    ic(self)
                    ic(self.remaining_quota)

                    yield self
                finally:
                    ic("Released shared lock object {0}.".format(self.shared_lock._lock))
                    # Lock is automatically released by with statement
        except SharedLockTimeoutError as e:
            ic(f"Sync lock acquisition timeout: {e}")
            raise QuotaSupervisorLockTimeoutError(str(e))

    async def async_reset(self, max_cap: int, time_frame: Optional[float] = 1):
        """
        Resets the state asynchronously using the SharedLock design.

        Returns:
            None
        """
        try:
            async with self.shared_lock:
                if ic((max_cap != self.max_cap) or (time_frame != self.time_frame)):
                    self.max_cap = max_cap
                    self.time_frame = time_frame
                    self.remaining_quota = max_cap
                    self.timestamp = None
                else:
                    logger = cast(logging.Logger, self.grab_logger())
                    logger.warning(
                        "Current instance ({0}) already has set max_cap = {1} and time_frame = {2}. "
                        "(Current remaining_quota value is {3}.)".format(
                            self, self.max_cap, self.time_frame, self.remaining_quota))
        except SharedLockTimeoutError as e:
            ic(f"Async reset lock acquisition timeout: {e}")
            raise QuotaSupervisorLockTimeoutError(str(e))

    def reset(self, max_cap: int, time_frame: Optional[float] = 1):
        """
        Resets the state synchronously using the SharedLock design.

        Returns:
            None
        """
        try:
            with self.shared_lock:
                if (max_cap != self.max_cap) or (time_frame != self.time_frame):
                    self.max_cap = max_cap
                    self.time_frame = time_frame
                    self.remaining_quota = max_cap
                    self.timestamp = None
                else:
                    logger = cast(logging.Logger, self.grab_logger())
                    logger.warning(
                        "Current instance ({0}) already has set max_cap = {1} and time_frame = {2}. "
                        "(Current remaining_quota value is {3}.)".format(
                            self, self.max_cap, self.time_frame, self.remaining_quota))
        except SharedLockTimeoutError as e:
            ic(f"Sync reset lock acquisition timeout: {e}")
            raise QuotaSupervisorLockTimeoutError(str(e))
