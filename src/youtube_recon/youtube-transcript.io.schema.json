{"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "microformat": {"type": "object", "properties": {"playerMicroformatRenderer": {"type": "object", "properties": {"thumbnail": {"type": "object", "properties": {"thumbnails": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "format": "url"}, "width": {"type": "integer"}, "height": {"type": "integer"}}, "required": ["url", "width", "height"]}}}, "required": ["thumbnails"]}, "embed": {"type": "object", "properties": {"iframeUrl": {"type": "string", "format": "url"}, "width": {"type": "integer"}, "height": {"type": "integer"}}, "required": ["iframeUrl", "width", "height"]}, "title": {"type": "object", "properties": {"simpleText": {"type": "string"}}, "required": ["simpleText"]}, "description": {"type": "object", "properties": {"simpleText": {"type": "string"}}, "required": ["simpleText"]}, "lengthSeconds": {"type": "string"}, "ownerProfileUrl": {"type": "string", "format": "url"}, "externalChannelId": {"type": "string"}, "isFamilySafe": {"type": "boolean"}, "availableCountries": {"type": "array", "items": {"type": "string"}}, "isUnlisted": {"type": "boolean"}, "hasYpcMetadata": {"type": "boolean"}, "viewCount": {"type": "string"}, "category": {"type": "string"}, "publishDate": {"type": "string", "format": "date-time"}, "ownerChannelName": {"type": "string"}, "uploadDate": {"type": "string", "format": "date-time"}, "isShortsEligible": {"type": "boolean"}, "externalVideoId": {"type": "string"}}, "required": ["thumbnail", "embed", "title", "description", "lengthSeconds", "ownerProfileUrl", "externalChannelId", "isFamilySafe", "availableCountries", "isUnlisted", "hasYpcMetadata", "viewCount", "category", "publishDate", "ownerChannelName", "uploadDate", "isShortsEligible", "externalVideoId"]}}, "required": ["playerMicroformatRenderer"]}, "tracks": {"type": "array", "items": {"type": "object", "properties": {"language": {"type": "string"}, "transcript": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "start": {"type": "string"}, "dur": {"type": "string"}}, "required": ["text", "start", "dur"]}}}, "required": ["language", "transcript"]}}, "languages": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "languageCode": {"type": "string"}}, "required": ["label", "languageCode"]}}, "isLive": {"type": "boolean"}, "playabilityStatus": {"type": "object"}, "author": {"type": "string"}, "channelId": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "title", "microformat", "tracks", "languages", "isLive", "playabilityStatus", "author", "channelId", "keywords"]}}