#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
from logear.autolog_exception import adapt_autolog_exception
from pydantic import BaseModel
from tqdm.asyncio import tqdm as atqdm
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Tuple, Union
from urllib3.util import Url
import yt_dlp


P = TypeVar("P", bound=BaseModel)
S = TypeVar("S", bound=BaseModel)


def create_model_instance(
    ret_type: Type[P], fields_data: Dict[str, Any], required_only: bool = False) -> P:
    
    kwargs = {}
    schema = ret_type.model_json_schema()
    properties = schema['required'] if required_only else list(schema['properties'].keys())
    for k in properties:
        kwargs[k] = fields_data[k]

    return ret_type(**kwargs)


@adapt_autolog_exception()
class VideoReconBase(ABC, Generic[P, S]):
    def __init__(self, prop_ret_type: Type[P], summary_ret_type: Type[S]):
        self.prop_ret_type = prop_ret_type
        self.summary_ret_type = summary_ret_type

    @staticmethod
    def get_video_url_from_id(video_id:str) -> Url:
        return Url(scheme="https", host="www.youtube.com", path="/watch", query="v={0}".format(video_id))

    @staticmethod
    def get_id_from_video_url(url: Url) -> str:
        return [p.replace('v=','') for p in url.query.split('&') if p.startswith('v=')][0]

    @abstractmethod
    def get_video_properties(self, url: Url) -> P:
        raise NotImplementedError()

    @abstractmethod
    def get_video_url(self, v_prop: P) -> Url:
        raise NotImplementedError()

    async def async_get_video_properties(self, url: Url) -> P:
        return self.get_video_properties(url=url)

    @abstractmethod
    def get_playlist_items(self, playlist: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[Union[Url, P]]:
        raise NotImplementedError()

    @abstractmethod
    async def async_get_playlist_items(self, playlist: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[Union[Url, P]]:
        raise NotImplementedError()

    @abstractmethod
    def get_video_summary(self, url:Url, prop:Optional[P]=None) -> S:
        raise NotImplementedError()

    async def async_get_video_summary(self, url:Url, prop:Optional[P]=None) -> S:
        return self.get_video_summary(url=url, prop=prop)

    @abstractmethod
    async def async_get_video_summaries(
            self, v_sources: Dict[Url, Union[P, None]]) -> Tuple[Dict[Url, S], Dict[Url, Exception]]:
        raise NotImplementedError()

    def get_video_meta(self, url: Url) -> Tuple[P, S]:
        video_props = self.get_video_properties(url=url)
        video_summary = self.get_video_summary(url=url)
        return video_props, video_summary
    
    async def async_get_video_meta(self, url: Url) -> Tuple[P, S]:
        tasks = [
            self.async_get_video_properties(url=url),
            self.async_get_video_summary(url=url)
        ]
        video_props, video_summary = await atqdm.gather(*tasks, desc="Fetching Video Data")
        
        return video_props, video_summary

    @abstractmethod
    async def async_batch_get_video_meta(
            self, urls_or_props: Tuple[Union[Url, P], ...]
    ) -> Tuple[Dict[Url, Tuple[P, S]], Dict[Url, Exception]]:
        raise NotImplementedError()
