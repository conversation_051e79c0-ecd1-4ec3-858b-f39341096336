#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
from confijector.config_loader import Config<PERSON>oa<PERSON>, config_bind_factory
from confijector.config_parser import Config<PERSON><PERSON><PERSON>, ConfigParserModule
from injector import ClassAssistedBuilder, inject, Injector, Module, provider, singleton

from pydantic import BaseModel
from typing import Any, Dict, Generic, List, Optional, Tuple, Type, TypeVar, Union
from urllib3.util import parse_url, Url
from youtube_recon import util
import yt_dlp


def get_playlist_meta(playlist_url: Url) -> Dict:
    ytdl_opts = {
        'nocheckcertificate': True,
        'ignoreerrors': True,
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False # changed from "in_playlist"
    }

    with yt_dlp.YoutubeDL(params=ytdl_opts) as ydl:
        playlist_meta = ydl.extract_info(playlist_url.url, download=False)
        if playlist_meta is None:
            raise ValueError(f"Failed to extract information for playlist: {playlist_url.url}")
        return playlist_meta


P = TypeVar("P", bound=BaseModel)


def get_video_list(playlist_url:Url, ret_type: P) -> List[P]:
    playlist_meta = get_playlist_meta(playlist_url=playlist_url)
    result = []
    for entry in playlist_meta['entries']:
        if entry is not None:  # Skip None entries (deleted videos)
            model_instance = util.create_model_instance_recursive(ret_type=ret_type, fields_data=entry)
            result.append(model_instance)
    return result


def get_video_url_list(playlist_url:Url) -> List[str]:
    video_urls = []
    playlist_meta = get_playlist_meta(playlist_url=playlist_url)
    for entry in playlist_meta['entries']:
        if entry is not None and 'webpage_url' in entry:  # Skip None entries and entries without webpage_url
            video_urls.append(entry['webpage_url'])

    return video_urls


class PlaylistReconBase(ABC, Generic[P]):
    def __init__(self, meta_ret_type: Type[P]):
        self.meta_ret_type = meta_ret_type

    @abstractmethod
    def get_playlist_meta(self, playlist_url: Url) -> Dict:
        raise NotImplementedError()

    @abstractmethod
    def get_video_list(self, playlist_url: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[P]:
        raise NotImplementedError()

    @abstractmethod
    def get_video_url_list(self, playlist_url: Url) -> List[str]:
        raise NotImplementedError()
