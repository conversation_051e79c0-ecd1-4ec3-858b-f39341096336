#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from logear.autolog_exception import adapt_autolog_exception
import logging
import requests
from tqdm import tqdm
from typing import Any, cast, List, Dict, Optional, Set

from youtube_recon import util
from youtube_recon.model.yt_transcript_io import Track, VideoDetail


@adapt_autolog_exception()
class YtTranscriptIoClient:
    """
    A client class for interacting with the video-details API of the youtube-transcript.io service.
    This service provides detailed information about YouTube videos, including available languages
    and transcripts, given a video ID. It can handle up to 50 YouTube video IDs per request.

    Details about the API can be found at https://www.youtube-transcript.io/api
    """
    def __init__(self, api_token: str, max_video_ids_per_request: int = 50):
        """
        Initializes the YtTranscriptIoClient with an API token and maximum video IDs per request.

        Args:
            api_token: The API token for accessing the youtube-transcript.io service.
            max_video_ids_per_request: The maximum number of video IDs that can be sent in a single API request.
                                         Defaults to 50.
        """
        self.max_video_ids_per_request = max_video_ids_per_request

        # Define the API call function as a method bound to the instance
        def _do_call_yt_transcript_io_api(self_ref, video_ids: Set[str]) -> List[Dict[str, Any]]:
            if len(video_ids) > self_ref.max_video_ids_per_request:
                raise ValueError(
                    "Number of video IDs ({0}) exceeds maximum allowed ({1})".format(
                        len(video_ids), self_ref.max_video_ids_per_request))

            response = requests.post(
                "https://www.youtube-transcript.io/api/video-details",
                headers={"Authorization": "Basic {0}".format(api_token),
                "Content-Type": "application/json"},
                json={"ids": list(video_ids)}
            )

            if response.status_code != 200:
                raise RuntimeError("Failed to get transcript of video(s): {0} - {1}".format(
                    response.status_code, response.text))

            return response.json()

        # Assign the method to the instance attribute
        self._do_call_yt_transcript_io_api = _do_call_yt_transcript_io_api.__get__(self, YtTranscriptIoClient)

    def call_yt_transcript_io_api(self, video_ids: Set[str]) -> List[Dict[str, Any]]:
        """
        Calls the youtube-transcript.io API to retrieve video details for the given video IDs.

        Args:
            video_ids: A set of YouTube video IDs to retrieve details for.

        Returns:
            A list of dictionaries, where each dictionary contains the video details for a single video.
        """
        return self._do_call_yt_transcript_io_api(video_ids=video_ids)

    def check_video_id_integrity(self, video_ids: Set[str], video_details: List[VideoDetail]) -> bool:
        """
        Checks the integrity of video IDs by comparing the requested IDs with the IDs present in the retrieved video details.

        Args:
            video_ids: The set of video IDs that were requested.
            video_details: The list of VideoDetail objects returned by the API.

        Returns:
            True if and only if:
            - All requested video IDs are present in the retrieved video details
            - No extra video IDs are present
            - No duplicate VideoDetail objects exist

        Logs warnings in the following scenarios:
            - Missing video IDs: Some requested video IDs are not in the retrieved details
            - Excess video IDs: More video details are returned than requested
            - Duplicate video details: The number of video IDs does not match the number of video details

        Note:
            Returns False and logs a warning if:
            - Any requested video ID is missing
            - Any extra video IDs are present
            - The number of video details does not match the number of unique video IDs
              (indicating potential duplicate VideoDetail objects)
        """
        
        if len(video_ids) != len(video_details):
            cast(logging.Logger, self.grab_logger()).warning(
                "Number of video IDs {0} does not match number of video details {1}".format(
                    video_ids, list(map(lambda video_detail: video_detail.id, video_details))))
            return False

        ids_in_details = set(map(lambda video_detail: video_detail.id, video_details))
        if ids_in_details != video_ids:
            missing_ids = video_ids - ids_in_details
            if len(missing_ids) > 0:
                cast(logging.Logger, self.grab_logger()).warning("Missing video IDs: {0}".format(missing_ids))
            excess_ids = ids_in_details - video_ids
            if len(excess_ids) > 0:
                cast(logging.Logger, self.grab_logger()).warning("Excess video IDs: {0}".format(excess_ids))
            return False

        return True

    def get_video_details(self, raw_video_details: List[Dict[str, Any]]) -> List[VideoDetail]:
        """
        Converts a list of raw video details (dictionaries) into a list of VideoDetail objects.

        Args:
            raw_video_details: A list of dictionaries, where each dictionary represents the raw video details
                               as returned by the API.

        Returns:
            A list of VideoDetail objects, where each object represents a single video's details.
        """
        video_details = []
        for video_data in tqdm(raw_video_details, desc="Processing each video data"):
            video_detail = util.create_model_instance_recursive(VideoDetail, video_data)
            video_details.append(video_detail)

        return video_details

    def get_joined_transcript(self,
                              video_detail: VideoDetail,
                              lang_code: str = "en",
                              context_length: int = -1) -> List[str]:
        """
        Extracts transcript text for a specific language, chunked by context length.

        Args:
            video_detail: The VideoDetail object containing transcript data.
            lang_code: The ISO 639-1 language code (e.g., "en", "es", "ja").
            context_length: The maximum character length for each transcript chunk.
                            If <= 0, the entire transcript is returned as one chunk.

        Returns:
            A list of transcript text chunks.

        Raises:
            ValueError: If the requested language code is not found in the video's available languages.
            LookupError: If the language is found but no corresponding transcript track exists.
            RuntimeError: If a single transcript item's text exceeds the context_length.
        """

        target_language_label: Optional[str] = None
        # Find the label corresponding to the language code (case-insensitive matching)
        for lang in video_detail.languages:
            if lang.languageCode.lower() == lang_code.lower():
                target_language_label = lang.label
                break

        if target_language_label is None:
            available_codes = [lang.languageCode for lang in video_detail.languages]
            available_codes.sort()
            raise ValueError(f"Language code '{lang_code}' not found for video ID {video_detail.id}. "
                             f"Available codes: {available_codes}")

        target_track: Optional[Track] = None
        # Find the track matching the language label
        for track in video_detail.tracks:
            # It's possible the label and track language might differ slightly (e.g., "English" vs "English (auto-generated)")
            # Using 'in' might be safer, but exact match seems intended by the structure. Let's stick to exact match for now.
            if track.language == target_language_label:
                target_track = track
                break

        if target_track is None:
            cast(logging.Logger, self.grab_logger()).warning(
                "Track for language '{0}' (code: {1}) not found for video ID {2}, "
                "although language is listed. Hence, returning empty transcript.".format(
                    target_language_label, lang_code, video_detail.id))
            return [] # Return empty list if track not found

        if not target_track.transcript:
            cast(logging.Logger, self.grab_logger()).warning(
                "Transcript list is empty for language '{0}' (code: {1}) for video ID {2}. "
                "Hence, returning empty transcript.".format(target_language_label, lang_code, video_detail.id))
            return [] # Return empty list if transcript list is empty


        # --- Process Transcript Items ---

        result_texts: List[str] = []

        if context_length <= 0:
            # Join all texts into a single string
            full_text = " ".join(item.text for item in target_track.transcript if item.text) # Added check for non-empty text
            if full_text: # Avoid adding an empty string if all items were empty
                 result_texts.append(full_text)
        else:
            # Chunk the text based on context_length
            current_chunk = ""
            for item in target_track.transcript:
                item_text = item.text.strip() # Strip whitespace from individual items
                if not item_text:
                    continue # Skip empty items

                item_text_len = len(item_text)

                # Check if the item itself exceeds the context length
                if item_text_len > context_length:
                    # TODO: Implement semantic chunking for long text items, potentially using LLMs,
                    # especially for languages without easy whitespace splitting.
                    raise RuntimeError(
                        "Single transcript item length ({0}) exceeds context_length ({1}) "
                        "for video ID {2}, language '{3}'. Text: '{4}'".format(
                            item_text_len, context_length, video_detail.id, target_language_label, item_text)
                    )

                # Calculate length if this item is added
                # Add 1 for the space separator if current_chunk is not empty
                separator_len = 1 if current_chunk else 0
                potential_len = len(current_chunk) + separator_len + item_text_len

                if potential_len <= context_length:
                    # Add to the current chunk
                    if current_chunk:
                        current_chunk += " " + item_text
                    else:
                        current_chunk = item_text
                else:
                    # Current chunk is full. Add it to results (if not empty)
                    if current_chunk:
                        result_texts.append(current_chunk)
                    # Start a new chunk with the current item
                    current_chunk = item_text

            # Add the last remaining chunk if it's not empty
            if current_chunk:
                result_texts.append(current_chunk)

        return result_texts

    async def async_call_yt_transcript_io_api(self, video_ids: Set[str]) -> List[Dict[str, Any]]:
        """
        Asynchronously calls the youtube-transcript.io API to retrieve video details for the given video IDs.

        Args:
            video_ids: A set of YouTube video IDs to retrieve details for.

        Returns:
            A list of dictionaries, where each dictionary contains the video details for a single video.
        """
        return self._do_call_yt_transcript_io_api(video_ids=video_ids)
