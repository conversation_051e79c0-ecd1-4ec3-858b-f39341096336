#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
from confijector.config_parser import ConfigParser
from google import genai
from google.genai import types
from injector import ClassAssistedBuilder, inject, Injector, Module, provider, singleton
import json
from logear.autolog_exception import adapt_autolog_exception
from logging import Logger
from pydantic import BaseModel
from quota_supervisor.quota_supervisor import QuotaSupervisor
import re
import time
from tqdm.asyncio import tqdm as atqdm
import traceback
from typing import cast, Dict, List, Optional, Tuple, Type, Union
from urllib3.util import parse_url, Url
from youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon
from youtube_recon.model.base_model_enhance import FieldRefBaseModel
from youtube_recon.model.ytdlp import VideoDataByYtDlp
from youtube_recon.video_recon import create_model_instance, VideoReconBase


class VideoSummary(FieldRefBaseModel):
    summary: str
    main_theme: str
    key_points: List[str]


class VideoData(VideoDataByYtDlp, VideoSummary):
    def __init__(self, yt_dlp_data: VideoDataByYtDlp, summary_data: VideoSummary):
        init_data = yt_dlp_data.model_dump()

        for name, value in summary_data.model_dump().items():
            if name not in init_data:
                init_data[name] = value

        # Call the parent BaseModel's __init__ with all the collected data
        # This will correctly initialize Pydantic's internal state, including
        # __pydantic_fields_set__, and perform validation.
        super().__init__(**init_data)


@adapt_autolog_exception()
class GeminiVideoRecon(VideoReconBase):
    def __init__(self,
                 ytdl_playlist_recon: YtdlPlaylistRecon,
                 api_key: str,
                 model: str='gemini-2.0-flash-lite',
                 max_cap: int=30,
                 time_frame: int=60,
                 rate_limit: int=1,
                 timeout: int=90,
                 lock_timeout: float=150.0):
        super().__init__(prop_ret_type=VideoDataByYtDlp, summary_ret_type=VideoSummary)
        self.ytdl_playlist_recon = ytdl_playlist_recon
        self.model = model
        # Use the original lock timeout - we'll handle lock timeouts properly with custom exceptions
        self.quota_supervisor = QuotaSupervisor(max_cap=max_cap, time_frame=time_frame, lock_timeout=lock_timeout)
        self.client = genai.Client(api_key=api_key)
        self.rate_limit = rate_limit
        self.timeout = timeout

    def get_prompt_for_summary(self, url:Url, prop:Optional[VideoDataByYtDlp] = None) -> str:
        prompt = """"Analyze the following YouTube video content. Provide a concise summary covering:

            1.  **Summary:** Provide a concise summary of the video content.
            2.  **Main Theme/Thesis/Claim:** What is the central point the creator is making?
            3.  **Key Points/Topics:** List the main subjects discussed, referencing specific theory, data, material, means, examples or technologies mentioned."""

        if prop is not None:
            prompt = prompt + """\n
            You may use these title and description for your analysis.
            - Video title: {0}
            - Description: {1}""".format(
                prop.fulltitle, prop.description)

        return prompt

    def cleanse_url(self, url: Url) -> Url:
        url_obj = url
        v_id_param = list(filter(lambda p: p.startswith('v='), url_obj.query.split('&')))[0]
        if v_id_param != url_obj.query:
            url_obj = parse_url(url=url_obj.url.replace(url_obj.query, v_id_param))

        return url_obj

    # overrides
    def get_video_properties(self, url: Url) -> VideoDataByYtDlp:
        ydl_opts = {}
        with self.ytdl_playlist_recon.get_ytdl() as ytdl:
            info = ytdl.extract_info(url.url, download=False)
            # makes the info json-serializable
            video_details = ytdl.sanitize_info(info)

        return create_model_instance(ret_type=self.prop_ret_type, fields_data=video_details)

    # overrides
    def get_video_url(self, v_prop: VideoDataByYtDlp) -> Url:
        return parse_url(url=v_prop.webpage_url)

    def get_content_type_data(self, url: Url, prop: Optional[VideoDataByYtDlp] = None) -> types.Content:
        url_obj = self.cleanse_url(url=url)
        return types.Content(
                    parts=[
                        types.Part(text=self.get_prompt_for_summary(url=url_obj, prop=prop)),
                        types.Part(
                            file_data=types.FileData(file_uri=url_obj.url)
                        )
                    ]
                )

    def get_content_config(self, clz: Type[BaseModel]) -> types.GenerateContentConfig:
        return types.GenerateContentConfig(
                    response_mime_type="application/json",
                    response_schema = clz
                )

    # overrides
    def get_video_summary(self, url:Url, prop:Optional[VideoDataByYtDlp] = None) -> VideoSummary:
        with self.quota_supervisor.consume(rate_limit=self.rate_limit):
            response = self.client.models.generate_content(
                model=self.model,
                contents=self.get_content_type_data(url=url, prop=prop),
                config=self.get_content_config(clz=VideoSummary)
            )
            return VideoSummary(**json.loads(response.text))

    # overrides
    async def async_get_video_summary(self, url:Url, prop:Optional[VideoDataByYtDlp] = None) -> VideoSummary:
        logger = cast(Logger, self.grab_logger())
        api_task = None

        try:
            async with self.quota_supervisor.async_consume(rate_limit=self.rate_limit):
                logger.debug("Calling Gemini to generate summary of {0}".format(url))

                # Create the API call task so we can cancel it if needed
                api_task: asyncio.Task[types.GenerateContentResponse] = asyncio.create_task(
                    self.client.aio.models.generate_content(
                        model=self.model,
                        contents=self.get_content_type_data(url=url),
                        config=self.get_content_config(clz=VideoSummary)
                    )
                )

                # Add timeout specifically for the Gemini API call
                try:
                    response: types.GenerateContentResponse = await asyncio.wait_for(api_task,
                                                                                     timeout=self.timeout)
                    return VideoSummary(**json.loads(response.text))

                except Exception as e:
                    logger.error("Gemini API call for {0} failed: {1}".format(
                        url, "".join(traceback.format_exception_only(type(e), e)).strip()))
                    # Cancel the API task
                    if api_task and not api_task.done():
                        api_task.cancel()
                    raise

        except Exception as e:
            logger.error("❌ Failed to get video summary for {0}: {1}".format(
                url, "".join(traceback.format_exception_only(type(e), e)).strip()))
            # Cancel the API task if it was created
            if api_task and not api_task.done():
                logger.debug("Cancelling API task for {0} due to exception. ".format(url))
                api_task.cancel()
                try:
                    await api_task
                except asyncio.CancelledError:
                    pass  # Expected when we cancel the task
            raise

    async def _async_get_video_summary_wrapper(
            self, url: Url, prop:Optional[VideoDataByYtDlp] = None
    ) -> Tuple[Url, Union[VideoSummary, None], Union[Exception, None]]:
        t0 = time.perf_counter()

        logger = cast(Logger, self.grab_logger())
        logger.debug("▶️ Start %s", url.url)

        try:
            v_summary = await self.async_get_video_summary(url=url, prop=prop)
            logger.debug(
                "✅ Done  %s in %.1fs", url.url, time.perf_counter() - t0)
            return url, v_summary, None

        except Exception as exc:
            if isinstance(exec, asyncio.TimeoutError):
                logger.error(
                    "⏰ Timeout after %.1fs for %s", self.timeout, url.url)
            else:
                logger.error(
                    "❌ Error   %s – %s",
                         url.url, "".join(traceback.format_exception_only(type(exc), exc)).strip())

            return url, None, exc

    # overrides
    async def async_get_video_summaries(
            self, v_sources: Dict[Url, Union[VideoDataByYtDlp, None]]
    ) -> Tuple[Dict[Url, VideoSummary], Dict[Url, Exception]]:
        logger = cast(Logger, self.grab_logger())

        # Limit concurrency to prevent overwhelming the quota supervisor
        max_concurrent = min(10, len(v_sources))  # Limit to 10 concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        async def limited_wrapper(url: Url, prop: Optional[VideoDataByYtDlp]):
            async with semaphore:
                return await self._async_get_video_summary_wrapper(url=url, prop=prop)

        logger.info(f"Processing {len(v_sources)} videos with max {max_concurrent} concurrent requests")

        tasks = []
        for url, v_prop in v_sources.items():
            tasks.append(asyncio.create_task(limited_wrapper(url=url, prop=v_prop)))

        result, errors = {}, {}

        # Simplified approach - just wait for all tasks to complete
        logger.info(f"Waiting for {len(tasks)} tasks to complete...")

        # Use as_completed with atqdm for progress display, but ensure proper cleanup
        completed_count = 0
        as_completed_iter = None
        active_coros = []

        try:
            as_completed_iter = asyncio.as_completed(tasks)
            for coro in atqdm(as_completed_iter, total=len(tasks), desc="Generating video summary"):
                active_coros.append(coro)
                try:
                    url, v_summary, err = await coro
                    completed_count += 1
                    logger.debug(f"Task {completed_count}/{len(tasks)} completed for {url}")

                    if err is None:
                        result[url] = v_summary
                    else:
                        errors[url] = err
                        logger.error(f"Task for {url} failed with error: {err}")

                except Exception as e:
                    completed_count += 1
                    logger.error(f"Unexpected error processing task {completed_count}/{len(tasks)}: {e}")
                    # We need to extract the URL from the exception context if possible
                    # For now, we'll use a generic key since we can't determine which URL failed
                    error_key = f"unknown_task_{completed_count}"
                    errors[error_key] = e
                finally:
                    # Clean up the coroutine to prevent hanging
                    if hasattr(coro, 'close'):
                        try:
                            coro.close()
                        except Exception as close_error:
                            logger.debug(f"Error closing coroutine: {close_error}")
                    # Remove from active list
                    if coro in active_coros:
                        active_coros.remove(coro)

        except Exception as e:
            logger.error(f"Critical error in async_get_video_summaries loop: {e}")
        finally:
            # Clean up any remaining active coroutines
            for coro in active_coros:
                if hasattr(coro, 'close'):
                    try:
                        coro.close()
                    except Exception as close_error:
                        logger.debug(f"Error closing remaining coroutine: {close_error}")

            # Cancel any remaining tasks
            for task in tasks:
                if not task.done():
                    task.cancel()

            # Wait for cancelled tasks to complete
            if any(not task.done() for task in tasks):
                try:
                    await asyncio.gather(*tasks, return_exceptions=True)
                except Exception as cleanup_error:
                    logger.error(f"Error during task cleanup: {cleanup_error}")

        logger.info(f"All {completed_count} tasks completed. Results: {len(result)}, Errors: {len(errors)}")

        return result, errors

    # overrides
    def get_playlist_items(self, playlist: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[VideoDataByYtDlp]:
        video_props = self.ytdl_playlist_recon.get_video_list(playlist_url=playlist, exclude=exclude)
        return video_props

    def get_video_props_urled(
            self, video_props: List[VideoDataByYtDlp]) -> Dict[Url, VideoDataByYtDlp]:
        urled_video_props = {}
        for video_prop in video_props:
            url = parse_url(url=cast(VideoDataByYtDlp, video_prop).webpage_url)
            urled_video_props.update({url: video_prop})

        return urled_video_props

    # overrides
    async def async_get_playlist_items(self, playlist: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[Union[Url, VideoDataByYtDlp]]:
        return self.get_playlist_items(playlist=playlist, exclude=exclude)

    async def async_get_video_meta(
            self, url_or_prop: Union[Url, VideoDataByYtDlp]
    ) -> Tuple[Url, Union[Tuple[VideoDataByYtDlp, VideoSummary], None], Union[Exception, None]]:
        url = url_or_prop if isinstance(url_or_prop, Url) else parse_url(url=url_or_prop.webpage_url)
        try:
            v_props = (await self.async_get_video_properties(url=url)) if isinstance(url_or_prop, Url) else url_or_prop
            v_summary = await self.async_get_video_summary(url=url)
            return url, (v_props, v_summary), None
        except Exception as e:
            return url, None, e

    # overrides
    async def async_batch_get_video_meta(
            self, urls_or_props: Tuple[Union[Url, VideoDataByYtDlp], ...]
    ) -> Tuple[Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]], Dict[Url, Exception]]:
        tasks = [asyncio.create_task(self.async_get_video_meta(url_or_prop=url_or_prop)) for url_or_prop in urls_or_props]

        result, errors = {}, {}
        for coro in atqdm(asyncio.as_completed(tasks), desc="Generating video summary"):
            url, (v_prop, v_summary), err = await coro
            if err is None:
                result.update({url: (v_prop, v_summary)})
            else:
                errors.update({url: err})

        return result, errors


class GeminiVideoReconProducer:
    @inject
    def __init__(self,
                 config_parser: ConfigParser,
                 ytdl_playlist_recon: YtdlPlaylistRecon,
                 builder: ClassAssistedBuilder[GeminiVideoRecon]):
        self.config_parser = config_parser
        self.ytdl_playlist_recon = ytdl_playlist_recon
        self.builder = builder

    def const_gemini_video_recon_params(self, gemini_config: dict) -> dict:
        params = {}
        kw = 'ytdl_playlist_recon'
        params.update({kw: self.ytdl_playlist_recon})
        kw = 'api_key'
        params.update({kw: gemini_config[kw]})
        kw = 'model'
        model = gemini_config[kw]
        params.update({kw: model})
        kw = 'quota'
        quota = gemini_config[kw][model]
        kw = 'max_cap'
        params.update({kw: int(re.findall('^[0-9]+', quota)[0])})
        kw = 'time_frame'
        params.update({kw: 60})
        kw = 'rate_limit'
        params.update({kw: 1})

        return params

    def add_gemini_video_recon_provider(self, injct: Injector, conf_gemini_key_seq: Tuple[str, ...]) -> Injector:
        gemini_config = cast(
            dict, self.config_parser.get_config_value_by_keys(key_sequence=list(conf_gemini_key_seq)))
        params = self.const_gemini_video_recon_params(gemini_config=gemini_config)
        builder = self.builder

        class GeminiVideoReconModule(Module):
            @singleton
            @provider
            def provide_gemini_video_recon(self) -> GeminiVideoRecon:
                return builder.build(**params)

        return injct.create_child_injector([GeminiVideoReconModule])
