#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import logging
logging.getLogger().setLevel(logging.DEBUG)

from icecream import ic
ic.disable()

import json
from abc import ABC, abstractmethod
from confijector.config_loader import Confi<PERSON><PERSON><PERSON><PERSON>, config_injector_factory
from confijector.config_parser import ConfigParser, ConfigParserModule
import functools
from injector import ClassAssistedBuilder, CallableProvider, ClassProvider, inject, Injector, Module, provider, singleton, SingletonScope
from logear.autolog_exception import adapt_autolog_exception
from pprint import pformat
import re
import sys
from typing import Any, cast, Dict, Iterable, List, Optional, Tuple, Type
from urllib3.util import parse_url, Url
import yt_dlp

from youtube_recon.util import unshorten_url
from youtube_recon.impl.gemini_video_recon import \
    GeminiVideoRecon, GeminiVideoReconProducer, VideoSummary, VideoData
from youtube_recon.impl.ytdlp_playlist_recon import YtdlProducer, YtdlPlaylistRecon, YtdlPlaylistReconProvider, add_ytdl_playlist_recon_provider
from youtube_recon.model.ytdlp import VideoDataByYtDlp
from youtube_recon.video_recon_cli import VideoReconCLIBase


@adapt_autolog_exception()
class VideoReconCLI(VideoReconCLIBase):
    @inject
    def __init__(self, video_recon: GeminiVideoRecon, attr_to_hide:Optional[Tuple[str, ...]] = None):
        super().__init__(video_recon=video_recon, prop_ret_type=VideoDataByYtDlp, summary_ret_type=VideoSummary)
        self.attr_to_hide = attr_to_hide

    @property
    def video_url_key(self) -> str:
        return 'video_URL'

    def move_url_to_front(self, v_meta: Dict[str, Any]) -> Dict[str, Any]:
        if self.video_url_key in v_meta:
            v = v_meta.pop(self.video_url_key)
            v_meta.pop(VideoDataByYtDlp.F.webpage_url.name, None)
        else:
            v = v_meta.pop(VideoDataByYtDlp.F.webpage_url.name, None)

        v_meta = {self.video_url_key: v, **v_meta}
        return v_meta

    def arrange_item(self, v_meta: Dict[str, Any]) -> Dict[str, Any]:
        v_data_dict = self.move_url_to_front(v_meta=v_meta)

        if self.attr_to_hide is not None:
            for k in self.attr_to_hide:
                v_data_dict.pop(k, None)

        return v_data_dict

    def gen_text_content(self, v_meta: Dict[str, Any]) -> str:
        contents = ""
        for k, v in v_meta.items():
            contents = contents + "{0}: {1}\n".format(
                re.sub('[_-]', ' ', (k[0].upper() + k[1:])),
                v if not isinstance(v, Iterable) else pformat(v, indent=4, width=sys.maxsize))

        return contents

    def gen_video_summary_dict(self, v_url: Url, v_summary: VideoSummary) -> Dict[str, Any]:
        v_summary_dict = v_summary.model_dump()
        v_summary_dict.update({self.video_url_key: v_url.url})
        v_summary_dict = self.arrange_item(v_meta=v_summary_dict)
        return v_summary_dict

    # overrides
    def list_video_summaries(self, summaries: Dict[Url, VideoSummary]) -> List[Dict[str, Any]]:
        contents_obj = []
        for url, v_summary in summaries.items():
            v_summary_dict = self.gen_video_summary_dict(v_url=url, v_summary=v_summary)
            contents_obj.append(v_summary_dict)
        return contents_obj

    def gen_summary_content(self, summaries: Dict[Url, VideoSummary], format: str = 'text') -> str:
        contents_obj = self.list_video_summaries(summaries=summaries)

        contents = ""
        if format == 'text':
            count = -1
            for v_summary_dict in contents_obj:
                count += 1
                contents = contents + self.gen_text_content(v_meta=v_summary_dict)
                if count < len(contents_obj) - 1:
                    contents += "\n"
        elif format == 'json':
            contents = json.dumps(contents_obj, indent=4, ensure_ascii=False)
        else:
            raise ValueError("Unsupported format: {0}".format(format))

        return contents

    # overrides
    def output_summary(self, summaries: Dict[Url, VideoSummary], path: Optional[str] = None, format: str = 'text'):
        contents = self.gen_summary_content(summaries=summaries, format=format)
        if path is None:
            print(contents)
        else:
            with open(path, 'wt') as fd:
                fd.write(contents)

    def set_attr_to_hide(self, attr_names: Tuple[str, ...]):
        self.attr_to_hide = attr_names

    def gen_video_detail_dict(self, yt_dlp_data: VideoDataByYtDlp, summary_data: VideoSummary) -> Dict[str, Any]:
        v_data = VideoData(yt_dlp_data=yt_dlp_data, summary_data=summary_data)
        v_data_dict = self.arrange_item(v_meta=v_data.model_dump())
        return v_data_dict

    def list_video_details(self, v_metas: Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]]) -> List[Dict[str, Any]]:
        contents_obj = []
        for url, (v_prop, v_summary) in v_metas.items():
            v_data_dict = self.gen_video_detail_dict(yt_dlp_data=v_prop, summary_data=v_summary)
            contents_obj.append(v_data_dict)
        return contents_obj

    def gen_full_info_content(
            self, meta: Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]], format: str = 'text') -> str:

        contents_obj = self.list_video_details(v_metas=meta)

        contents = ""
        if format == 'text':
            count = -1
            for v_data_dict in contents_obj:
                count += 1
                contents = contents + self.gen_text_content(v_meta=v_data_dict)
                if count < len(contents_obj) - 1:
                    contents += "\n"
        elif format == 'json':
            contents = json.dumps(contents_obj, indent=4, ensure_ascii=False)
        else:
            raise ValueError("Unsupported format: {0}".format(format))

        return contents

    # overrides
    def output_detail(self,
                      meta: Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]],
                      path: Optional[str] = None,
                      format: str = 'text'):
        contents = self.gen_full_info_content(meta=meta, format=format)
        if path is None:
            print(contents)
        else:
            with open(path, 'wt') as fd:
                fd.write(contents)

    @property
    def playlist_key(self) -> str:
        return 'playlist'

    @property
    def playlist_url_key(self) -> str:
        return 'URL'

    @property
    def playlist_video_list_key(self) -> str:
        return 'video'

    # overrides
    def output_playlist_urls(self,
                             playlist: Url,
                             props: Tuple[VideoDataByYtDlp],
                             path: Optional[str] = None,
                             format: str = 'text',
                             extra: bool=False):
        attrs_to_show = ['webpage_url', 'fulltitle', 'duration', 'description']
        if self.attr_to_hide is not None:
            for attr in set(attrs_to_show).intersection(set(self.attr_to_hide)):
                attrs_to_show.remove(attr)

        output_dict = {self.playlist_key: {self.playlist_url_key: playlist.url}}
        if extra:
            details = []
            for i, v_prop in enumerate(props):
                v_data = {}
                for attr in attrs_to_show:
                    v_data[attr] = getattr(v_prop, attr)
                details.append(v_data)

            output_dict[self.playlist_key].update({self.playlist_video_list_key: details})

        else:
            output_dict[self.playlist_key].update({self.playlist_video_list_key: [prop.webpage_url for prop in props]})

        contents = ""
        if format == 'text':
            contents += "Playlist: {0}\n\n".format(output_dict[self.playlist_key][self.playlist_url_key])
            for i, v_meta in enumerate(output_dict[self.playlist_key][self.playlist_video_list_key]):
                if extra:
                    contents += self.gen_text_content(v_meta=v_meta)
                    if i < len(v_meta) - 1:
                        contents += "\n"
                else:
                    contents += "{0}\n".format(v_meta)

        elif format == 'json':
            contents = json.dumps(output_dict, indent=4, ensure_ascii=False)

        else:
            raise ValueError(f"Unsupported format: {format}")

        if path is None:
            print(contents)
        else:
            with open(path, 'wt') as fd:
                fd.write(contents)

    def output_video_summaries_in_playlist(self,
                                           playlist: Url,
                                           summaries: Dict[Url, VideoSummary],
                                           path: Optional[str] = None,
                                           format: str = 'text'):
        # This is similar to output_summary but with playlist context
        output_dict = {self.playlist_key: {self.playlist_url_key: playlist.url}}

        contents_obj = self.list_video_summaries(summaries=summaries)
        output_dict[self.playlist_key].update({self.playlist_video_list_key: contents_obj})

        contents = ""
        if format == 'text':
            contents = "Summaries for playlist: {0}\n\n".format(output_dict[self.playlist_key][self.playlist_url_key])
            count = -1
            for v_summary_dict in output_dict[self.playlist_key][self.playlist_video_list_key]:
                count += 1
                contents = contents + self.gen_text_content(v_meta=v_summary_dict)
                if count < len(output_dict[self.playlist_key][self.playlist_video_list_key]) - 1:
                    contents += "\n"
        elif format == 'json':
            contents = json.dumps(output_dict, indent=4, ensure_ascii=False)
        else:
            raise ValueError("Unsupported format: {0}".format(format))

        if path is None:
            print(contents)
        else:
            with open(path, 'wt') as fd:
                fd.write(contents)

    def gen_playlist_video_detail_dict(
            self, playlist: Url, v_metas: Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]]) -> Dict[str, Any]:
        output_dict = {self.playlist_key: {self.playlist_url_key: playlist.url}}

        contents_obj = self.list_video_details(v_metas=v_metas)
        output_dict[self.playlist_key].update({self.playlist_video_list_key: contents_obj})

        return output_dict

    # overrides
    def output_video_details_in_playlist(self,
                                         playlist: Url,
                                         v_metas: Dict[Url, Tuple[VideoDataByYtDlp, VideoSummary]],
                                         path: Optional[str] = None,
                                         format: str = 'text'):
        output_dict = self.gen_playlist_video_detail_dict(playlist=playlist, v_metas=v_metas)

        contents = ""
        if format == 'text':
            contents = "Playlist: {0}\n\n".format(output_dict[self.playlist_key][self.playlist_url_key])
            count = -1
            for v_data_dict in output_dict[self.playlist_key][self.playlist_video_list_key]:
                count += 1
                contents = contents + self.gen_text_content(v_meta=v_data_dict)

                if count < len(output_dict[self.playlist_key][self.playlist_video_list_key]) - 1:
                    contents += "\n"
        elif format == 'json':
            contents = json.dumps(output_dict, indent=4, ensure_ascii=False)
        else:
            raise ValueError("Unsupported format: {0}".format(format))

        if path is None:
            print(contents)
        else:
            with open(path, 'wt') as fd:
                fd.write(contents)

    # overrides
    def validate_url(self, url: str) -> Url:
        url_obj = parse_url(url=url)
        if "www.youtube.com" != url_obj.host:
            url_obj = unshorten_url(url=url_obj)
            if "www.youtube.com" != url_obj.host:
                raise ValueError("Invalid YouTube URL: {0}".format(url))

        return url_obj


class GeminiVideoReconCLIProducer:
    @inject
    def __init__(self, config_parser: ConfigParser, builder: ClassAssistedBuilder[VideoReconCLI]):
        self.config_parser = config_parser      # This config_parser can be from different Injector instance.
        self.builder = builder

    def add_gemini_video_recon_cli_provider(
            self, injct: Injector, ytdlp_mask_conf_key_seq: Tuple[str, ...]) -> Injector:
        ytdlp_mask_conf_key_seq = self.config_parser.get_config_value_by_keys(key_sequence=list(ytdlp_mask_conf_key_seq))
        builder = self.builder

        class GeminiVideoReconCLIModule(Module):
            @singleton
            @provider
            def provide_gemini_video_recon_cli(self) -> VideoReconCLI:
                return builder.build(video_recon=injct.get(GeminiVideoRecon),
                                     attr_to_hide=tuple(ytdlp_mask_conf_key_seq))

        return injct.create_child_injector([GeminiVideoReconCLIModule()])


if __name__ == "__main__":
    configs = [ConfigLoader.get_default_conf_file()]

    injector_obj = config_injector_factory(conf_files=configs)
    injector_obj = add_ytdl_playlist_recon_provider(injct=injector_obj,
                                                    conf_ytdlp_cli_opts_key_seq=('youtube_recon', 'cli', 'ytdlp', 'cli_opts'),
                                                    ytdl_log_level=logging.INFO)
    injector_obj = injector_obj.get(GeminiVideoReconProducer).add_gemini_video_recon_provider(
        injct=injector_obj, conf_gemini_key_seq=('youtube_recon', 'gemini'))
    injector_obj = injector_obj.get(GeminiVideoReconCLIProducer).add_gemini_video_recon_cli_provider(
        injct=injector_obj, ytdlp_mask_conf_key_seq=('youtube_recon', 'cli', 'ytdlp', 'hide'))

    cli = injector_obj.get(VideoReconCLI)
    cli_logger = cast(logging.Logger, cli.grab_logger())
    cli_logger.setLevel(logging.DEBUG)
    ic.configureOutput(includeContext=True,
                       outputFunction=(lambda s: cli_logger.debug(s)))
    ic.enable()
    cli.run()
