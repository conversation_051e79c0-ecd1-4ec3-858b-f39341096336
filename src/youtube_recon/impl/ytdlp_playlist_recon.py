#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
from confijector.config_loader import Config<PERSON>oa<PERSON>, config_bind_factory
from confijector.config_parser import Config<PERSON><PERSON><PERSON>, ConfigParserModule
from icecream import ic
from injector import Binder, CallableProvider, ClassAssistedBuilder, inject, Injector, Module, provider, singleton, SingletonScope
import json
from logear.autolog_exception import adapt_autolog_exception
import logging
from typing import Any, cast, Dict, List, Optional, Tuple, Type, Union
from urllib3.util import parse_url, Url
import yt_dlp

from youtube_recon import util
from youtube_recon.model.ytdlp import VideoDataByYtDlp
from youtube_recon.yt_playlist_recon import PlaylistReconBase


@adapt_autolog_exception()
class YtdlLogger:
    """
    Local yt_dlp Logger class that bridges yt_dlp logging to our logging system.

    This class implements the yt_dlp logger interface and forwards log messages
    to logger instance automatically attached by adapt_autolog_exception decorator and
    accessible via grab_logger method.
    """

    def debug(self, msg):
        # For compatibility with youtube-dl, both debug and info are passed into debug
        # You can distinguish them by the prefix '[debug] '
        if msg.startswith('[debug] '):
            self.grab_logger().debug(f"{msg}")
        else:
            self.grab_logger().info(f"{msg}")

    def info(self, msg):
        self.grab_logger().info(f"{msg}")

    def warning(self, msg):
        self.grab_logger().warning(f"{msg}")

    def error(self, msg):
        self.grab_logger().error(f"{msg}")


class YtdlProducer:
    @classmethod
    @property
    def ytdlp_cli_default_options(cls) -> Dict[str, Any]:
        if not hasattr(cls, '_ytdlp_cli_default_options'):
            cls._ytdlp_cli_default_options = yt_dlp.options.create_parser().get_default_values().__dict__
        return cls._ytdlp_cli_default_options

    @classmethod
    def parse_ytdlp_cli_options(cls, cli_opts: List[Union[str, bool, int, float]]) -> Dict[str, Any]:
        api_opts = ic(yt_dlp.parse_options(cli_opts).ydl_opts)
        default_keys = set(cls.ytdlp_cli_default_options.keys())
        unique_keys = set(api_opts.keys()) - default_keys
        common_keys = set(api_opts.keys()).intersection(default_keys)

        diff = {key: api_opts[key] for key in unique_keys}
        diff.update({key: api_opts[key] for key in common_keys if api_opts[key] != cls.ytdlp_cli_default_options[key]})
        if 'postprocessors' in diff and 'postprocessors' in cls.ytdlp_cli_default_options:
            diff['postprocessors'] = [pp for pp in diff['postprocessors']
                                      if pp not in cls.ytdlp_cli_default_options['postprocessors']]

        return diff

    @inject
    def __init__(self, config_parser: ConfigParser, builder: ClassAssistedBuilder[yt_dlp.YoutubeDL]):
        self.config_parser = config_parser      # This config_parser can be from different Injector instance.
        self.builder = builder

    def add_ytdl_provider(self,
                          injct: Injector,
                          conf_ytdlp_cli_opts_key_seq: Tuple[str, ...],
                          ytdl_log_level: int = logging.INFO) -> Injector:
        ytdlp_cli_opts_config = self.config_parser.get_config_value_by_keys(
            key_sequence=list(conf_ytdlp_cli_opts_key_seq))

        api_opts = ic(self.parse_ytdlp_cli_options(cli_opts=ytdlp_cli_opts_config))
        api_opts['extract_flat'] = 'False'  # Not setting to False causes failure of filling fields in VideoDataByYtDlp
        ytdl_logger = YtdlLogger()
        cast(logging.Logger, ytdl_logger.grab_logger()).setLevel(ytdl_log_level)
        api_opts['logger'] = YtdlLogger()

        builder = self.builder

        # Create a module that binds the YoutubeDL instance as a singleton
        class YtdlModule(Module):
            @singleton
            @provider
            def provide_youtubedl(self) -> yt_dlp.YoutubeDL:
                return builder.build(**{'params': api_opts})

        return injct.create_child_injector([YtdlModule()])


@adapt_autolog_exception()
class YtdlPlaylistRecon(PlaylistReconBase):
    @inject
    def __init__(self, ytdl: yt_dlp.YoutubeDL):
        super().__init__(meta_ret_type=VideoDataByYtDlp)
        self.ytdl = ytdl

    def get_ytdl(self) -> yt_dlp.YoutubeDL:
        return self.ytdl

    # overrides
    def get_playlist_meta(self, playlist_url: Url) -> Dict:
        return self.ytdl.extract_info(playlist_url.url, download=False)

    # overrides
    def get_video_list(self, playlist_url: Url, exclude: Optional[Union[str, Tuple[str, ...]]] = None) -> List[VideoDataByYtDlp]:
        playlist_meta: dict = self.get_playlist_meta(playlist_url=playlist_url)
        cast(logging.Logger, self.grab_logger()).debug(
            "Entries in playlist {0}:\n {1}".format(playlist_url.url, playlist_meta.get('entries', [])))

        # Convert exclude parameter to a set for efficient lookup
        exclude_ids = set()
        if exclude is not None:
            if isinstance(exclude, str):
                exclude_ids.add(exclude)
            else:  # Tuple[str, ...]
                exclude_ids.update(exclude)

        result = []
        for entry in playlist_meta['entries']:
            if entry is not None:  # Skip None entries (deleted videos)
                model_instance = util.create_model_instance_recursive(ret_type=VideoDataByYtDlp, fields_data=entry)
                # Check if this video should be excluded
                if model_instance.id and model_instance.id in exclude_ids:
                    cast(logging.Logger, self.grab_logger()).debug(
                        "Excluding video with ID {0} from playlist {1}".format(model_instance.id, playlist_url.url))
                    continue

                result.append(model_instance)
        return result

    # overrides
    def get_video_url_list(self, playlist_url: Url) -> List[str]:
        video_urls = []
        playlist_meta = self.get_playlist_meta(playlist_url=playlist_url)
        for entry in playlist_meta['entries']:
            if entry is not None and VideoDataByYtDlp.F.webpage_url.name in entry:  # Skip None entries and entries without webpage_url
                video_urls.append(entry[VideoDataByYtDlp.F.webpage_url.name])

        return video_urls


class YtdlPlaylistReconProvider(Module):
    @singleton
    @provider   # @provider works as @inject too
    def provide_ytdl_playlist_recon(self, ytdl: yt_dlp.YoutubeDL, builder: ClassAssistedBuilder[YtdlPlaylistRecon]) -> YtdlPlaylistRecon:
        return builder.build(ytdl=ytdl)


def add_ytdl_playlist_recon_provider(injct: Injector,
                                     conf_ytdlp_cli_opts_key_seq: Tuple[str, ...],
                                     ytdl_log_level: int = logging.INFO) -> Injector:

    class _YtdlPlaylistReconProvider(Module):
        @classmethod
        @property
        def ytdlp_cli_default_options(cls) -> Dict[str, Any]:
            if not hasattr(cls, '_ytdlp_cli_default_options'):
                cls._ytdlp_cli_default_options = yt_dlp.options.create_parser().get_default_values().__dict__
            return cls._ytdlp_cli_default_options

        @classmethod
        def parse_ytdlp_cli_options(cls, cli_opts: List[Union[str, bool, int, float]]) -> Dict[str, Any]:
            api_opts = ic(yt_dlp.parse_options(cli_opts).ydl_opts)
            default_keys = set(cls.ytdlp_cli_default_options.keys())
            unique_keys = set(api_opts.keys()) - default_keys
            common_keys = set(api_opts.keys()).intersection(default_keys)

            diff = {key: api_opts[key] for key in unique_keys}
            diff.update(
                {key: api_opts[key] for key in common_keys if api_opts[key] != cls.ytdlp_cli_default_options[key]})
            if 'postprocessors' in diff and 'postprocessors' in cls.ytdlp_cli_default_options:
                diff['postprocessors'] = [pp for pp in diff['postprocessors']
                                          if pp not in cls.ytdlp_cli_default_options['postprocessors']]

            return diff

        @inject
        def __init__(self, config_parser: ConfigParser, builder: ClassAssistedBuilder[YtdlPlaylistRecon]):
            self.config_parser = config_parser  # This config_parser can be from different Injector instance.
            self.builder = builder

        def configure(self, binder: Binder):
            ytdlp_cli_opts_config = self.config_parser.get_config_value_by_keys(
                key_sequence=list(conf_ytdlp_cli_opts_key_seq))

            api_opts = ic(self.parse_ytdlp_cli_options(cli_opts=ytdlp_cli_opts_config))
            api_opts[
                'extract_flat'] = 'False'  # Not setting to False causes failure of filling fields in VideoDataByYtDlp
            ytdl_logger = YtdlLogger()
            cast(logging.Logger, ytdl_logger.grab_logger()).setLevel(ytdl_log_level)
            api_opts['logger'] = YtdlLogger()

            def yt_dlp_factory() -> yt_dlp.YoutubeDL:
                return yt_dlp.YoutubeDL(**{'params': api_opts})

            singleton_scope = SingletonScope(injector=injct)
            provider = CallableProvider(yt_dlp_factory)
            singleton_provider = singleton_scope.get(yt_dlp.YoutubeDL, provider)
            binder.bind(yt_dlp.YoutubeDL, to=singleton_provider, scope=singleton)

        @provider
        def provide_ytdl_playlist_recon(self, ytdl: yt_dlp.YoutubeDL, builder: ClassAssistedBuilder[YtdlPlaylistRecon]) -> YtdlPlaylistRecon:
            return builder.build(ytdl=ytdl)

    ytdl_playlist_recon_provider = injct.get(_YtdlPlaylistReconProvider)
    return injct.create_child_injector([ytdl_playlist_recon_provider])

