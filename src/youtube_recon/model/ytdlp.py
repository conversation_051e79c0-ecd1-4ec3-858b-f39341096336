from typing import List, Optional

from youtube_recon.model.base_model_enhance import FieldRefBaseModel


class VideoDataByYtDlp(FieldRefBaseModel):
    categories: List[str]
    channel: str
    description: str
    duration: int
    fulltitle: str
    id: str
    language: Optional[str] = None
    tags: List[str]
    thumbnail: str
    timestamp: int
    uploader: str
    uploader_url: str
    webpage_url: str
