#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from typing import List, Dict
from pydantic import BaseModel, HttpUrl, RootModel

from youtube_recon.model.base_model_enhance import FieldRefBaseModel

class Thumbnail(FieldRefBaseModel):
    url: HttpUrl
    width: int
    height: int


class Embed(FieldRefBaseModel):
    iframeUrl: HttpUrl
    width: int
    height: int


class Title(FieldRefBaseModel):
    simpleText: str


class Description(FieldRefBaseModel):
    simpleText: str


class TranscriptItem(FieldRefBaseModel):
    text: str
    start: str
    dur: str


class Track(FieldRefBaseModel):
    language: str
    transcript: List[TranscriptItem]


class Language(FieldRefBaseModel):
    label: str
    languageCode: str


class PlayerMicroformatRenderer(FieldRefBaseModel):
    thumbnail: Dict[str,List[Thumbnail]]
    embed: Embed
    title: Title
    description: Description
    lengthSeconds: str
    ownerProfileUrl: HttpUrl
    externalChannelId: str
    isFamilySafe: bool
    availableCountries: List[str]
    isUnlisted: bool
    hasYpcMetadata: bool
    viewCount: str
    category: str
    publishDate: datetime
    ownerChannelName: str
    uploadDate: datetime
    isShortsEligible: bool
    externalVideoId: str


class Microformat(FieldRefBaseModel):
    playerMicroformatRenderer: PlayerMicroformatRenderer


class VideoDetail(FieldRefBaseModel):
    id: str
    title: str
    microformat: Microformat
    tracks: List[Track]
    languages: List[Language]
    isLive: bool
    playabilityStatus: Dict
    author: str
    channelId: str
    keywords: List[str]


class VideoDetailsList(FieldRefBaseModel):
    root: List[VideoDetail]
