#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel
from pydantic.fields import FieldInfo # Correct import for Pydantic V2
from typing import Any, TypeVar, Generic

# Helper class to provide the .name attribute
_T = TypeVar('_T')
class FieldNameProvider(Generic[_T]):
    def __init__(self, name: str, field_info: FieldInfo):
        self._name = name
        self._field_info = field_info

    @property
    def name(self) -> str:
        return self._name

    def __str__(self) -> str:
        return self._name

    def __repr__(self) -> str:
        return f"<FieldName: {self._name}>"

    def __format__(self, format_spec: str) -> str:
        return self._name.__format__(format_spec)

    def __hash__(self) -> int:
        return hash(self._name)

    def __eq__(self, other: Any) -> bool:
        if isinstance(other, FieldNameProvider):
            return self._name == other._name
        if isinstance(other, str):
            return self._name == other
        return NotImplemented

# A simple namespace class to hold FieldNameProvider instances
class FieldsNamespace:
    pass

# Custom base model
class FieldRefBaseModel(BaseModel):

    @classmethod # <--- THIS IS THE CRUCIAL FIX
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:
        # Now, when Pydantic's metaclass calls this via super(),
        # 'cls' will correctly be the subclass (e.g., VideoDataByYtDlp2)

        # It's good practice to call super() in __pydantic_init_subclass__
        # if there's a chance of further subclassing or multiple inheritance
        # involving other bases that might also define __pydantic_init_subclass__.
        # For a direct Pydantic BaseModel subclass, Pydantic handles its own
        # core __pydantic_init_subclass__ logic before calling yours.
        # However, to be robust for more complex hierarchies:
        super().__pydantic_init_subclass__(**kwargs)


        _F_CLASS_NAME = f'_{cls.__name__}FieldsRefContainer'
        _F_Type = type(_F_CLASS_NAME, (FieldsNamespace,), {})
        f_instance = _F_Type()

        if hasattr(cls, 'model_fields') and cls.model_fields:
            for field_name_str, field_info_obj in cls.model_fields.items():
                provider = FieldNameProvider(field_name_str, field_info_obj)
                setattr(f_instance, field_name_str, provider)
        else:
            print(f"Warning: No model_fields found for {cls.__name__} in __pydantic_init_subclass__")

        setattr(cls, 'F', f_instance)
