#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from icecream import ic
import inspect
from typing import Any, Dict, Generic, List, Type, TypeVar, Union, get_origin, get_args
from pydantic import BaseModel, ValidationError
import requests
from urllib3.util import parse_url, Url


def unshorten_url(url: Url) -> Url:
    headers = {'User-Agent': 'Mozilla/5.0'}
    response = requests.get(url, headers=headers, timeout=10, allow_redirects=False)
    if response.ok:
        if 300 <= response.status_code < 400:
            if 'Location' in response.headers:
                return parse_url(url=response.headers['Location'])
        return url

    response.raise_for_status()


# Helper type variable
P = TypeVar("P", bound=BaseModel)


def _is_pydantic_model(type_hint: Any) -> bool:
    """Checks if a type hint is a Pydantic BaseModel class."""
    return inspect.isclass(type_hint) and issubclass(type_hint, BaseModel)

def _get_base_model_type(type_hint: Any) -> Union[Type[BaseModel], None]:
    """
    Extracts the BaseModel type from potentially generic hints like Optional[Model].
    Returns None if no BaseModel is found.
    """
    origin = get_origin(type_hint)
    args = get_args(type_hint)

    if _is_pydantic_model(type_hint):
        return type_hint
    # Handle Optional[Model] -> Union[Model, NoneType]
    elif origin is Union:
        for arg in args:
            if _is_pydantic_model(arg):
                return arg
    # Add checks for other generics if necessary, e.g., custom ones
    return None

def create_model_instance_recursive(
    ret_type: Type[P],
    fields_data: Union[str, Dict[str, Any]], # Accepts JSON string initially, dict recursively
    required_only: bool = False
) -> P:
    """
    Creates an instance of a Pydantic model from JSON string or dictionary,
    handling nested models recursively based on type hints.

    Args:
        ret_type: The Pydantic model class (e.g., MyModel).
        fields_data: A JSON string (for initial call) or a dictionary
                     (for recursive calls) containing field data.
        required_only: If True, only processes fields marked as required
                       in the model schema. Otherwise, processes all fields
                       present in the input data that correspond to model fields.

    Returns:
        An instance of the specified Pydantic model (ret_type).

    Raises:
        ValueError: If fields_data is a string but not valid JSON.
        TypeError: If fields_data is not a string or dictionary.
        KeyError: If required_only is True and a required field is missing
                  in the corresponding input data level.
        ValidationError: If Pydantic validation fails during instantiation.
        Exception: For other potential errors during processing or instantiation.
    """
    # 1. Parse JSON string or use dictionary directly
    if isinstance(fields_data, str):
        try:
            data_dict = json.loads(fields_data)
            if not isinstance(data_dict, dict):
                 raise ValueError("JSON string must decode into a dictionary (object).")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON string provided: {e}") from e
    elif isinstance(fields_data, dict):
        data_dict = fields_data # Use dict directly (for recursive calls)
    else:
        raise TypeError("fields_data must be a JSON string or a dictionary.")

    kwargs: Dict[str, Any] = {}
    model_fields = ret_type.model_fields # Pydantic v2+ access to fields

    # 2. Determine fields to process based on required_only flag
    if required_only:
        fields_to_process = {
            name for name, field_info in model_fields.items() if field_info.is_required()
        }
    else:
        # Process all keys present in the input data that are also model fields
        fields_to_process = set(model_fields.keys()) & set(data_dict.keys())
        # Note: If not required_only, we don't raise KeyError for missing fields here;
        # Pydantic handles defaults or optionality during final instantiation.

    # 3. Iterate and process fields
    for field_name in fields_to_process:
        if field_name not in data_dict:
            # This check is mainly relevant when required_only=True
            if required_only:
                 # We already know this field is required from the fields_to_process logic
                 raise KeyError(
                     f"Required field '{field_name}' missing in input data "
                     f"for model {ret_type.__name__}."
                 )
            continue # Skip if not required_only and data is missing

        field_value = ic(data_dict[ic(field_name)])
        field_info = ic(model_fields[field_name])
        field_type_hint = ic(field_info.annotation)

        # Get generic origin (like List, Dict, Union) and args (like Model in List[Model])
        origin = ic(get_origin(field_type_hint))
        args = ic(get_args(field_type_hint))

        # Attempt to find a base Pydantic model type within the hint
        nested_model_type = ic(_get_base_model_type(field_type_hint))

        try:
            # 4. Handle recursion for nested structures
            if nested_model_type and field_value is not None:
                # Case 1: Direct nested model (or Optional[Model])
                if isinstance(field_value, dict):
                    kwargs[field_name] = create_model_instance_recursive(
                        nested_model_type, field_value, required_only
                    )
                else:
                    # Pass non-dict value; Pydantic validation will catch type errors
                    kwargs[field_name] = field_value

            elif origin in (list, List) and args and field_value is not None:
                # Case 2: List containing models (e.g., List[Model])
                list_item_type = args[0]
                nested_list_item_model_type = _get_base_model_type(list_item_type)
                if nested_list_item_model_type and isinstance(field_value, list):
                    processed_list = []
                    for item in field_value:
                        if isinstance(item, dict):
                             processed_list.append(
                                 create_model_instance_recursive(
                                     nested_list_item_model_type, item, required_only
                                 )
                             )
                        else:
                             # Append non-dict item; Pydantic validation handles list item types
                             processed_list.append(item)
                    kwargs[field_name] = processed_list
                else:
                    # List of non-models or non-list data
                    kwargs[field_name] = field_value

            elif origin in (dict, Dict) and args and len(args) == 2 and field_value is not None:
                # Case 3: Dict with model values (e.g., Dict[str, Model])
                dict_value_type = args[1] # Type of the dictionary values
                nested_dict_value_model_type = _get_base_model_type(dict_value_type)
                if nested_dict_value_model_type and isinstance(field_value, dict):
                    processed_dict = {}
                    for key, value in field_value.items():
                         if isinstance(value, dict):
                             processed_dict[key] = create_model_instance_recursive(
                                 nested_dict_value_model_type, value, required_only
                             )
                         else:
                             # Assign non-dict value; Pydantic validation handles dict value types
                             processed_dict[key] = value
                    kwargs[field_name] = processed_dict
                else:
                     # Dict of non-models or non-dict data
                     kwargs[field_name] = field_value

            else:
                # Case 4: Primitive types, simple collections, or non-Pydantic objects
                kwargs[field_name] = field_value

        except ValidationError as e:
            # --- FIX START ---
            # Catch ValidationError specifically.
            # Re-raise the original error 'e' directly.
            # Pydantic v2's ValidationError contains rich error details.
            # Trying to recreate it with just a string causes the TypeError.
            # We add context to the exception chain using 'from e',
            # but raise the original 'e' to preserve its details.
            # You could wrap it in a different exception if preferred,
            # but re-raising 'e' is often clearest.
            # print(f"DEBUG: Caught ValidationError processing field '{field_name}' in {ret_type.__name__}") # Optional debug
            # print(f"DEBUG: Error details: {e.errors()}") # Optional debug
            raise e # Re-raise the original validation error
            # --- FIX END ---

        except (KeyError, ValueError, TypeError) as e:
            # Keep the original re-raising logic for other error types,
            # assuming their constructors *can* typically take a single string.
            # If other custom exceptions cause TypeErrors here, they'd need specific handling too.
            try:
                raise type(e)(f"Error processing field '{field_name}' in {ret_type.__name__}: {e}") from e
            except TypeError:
                 # Fallback for unexpected constructor issues with other types
                 raise RuntimeError(f"Error processing field '{field_name}' in {ret_type.__name__}: {e}") from e

    # 5. Instantiate the final model
    try:
        # This is where the ValidationError for PlayerMicroformatRenderer originates
        instance = ret_type(**kwargs)
        return instance
    except ValidationError as e:
        # This block catches validation errors during the *final* instantiation
        # of the current ret_type. If the error happened during a *recursive*
        # call's final instantiation (like for PlayerMicroformatRenderer),
        # it would have been caught and re-raised by the 'except ValidationError' above.
        # print(f"DEBUG: Final validation failed for {ret_type.__name__} with kwargs: {kwargs}") # Optional debug
        # print(f"DEBUG: Final validation errors: {e.errors()}") # Optional debug
        raise e # Re-raise the original validation error
    except Exception as e:
        # Catch other potential errors during instantiation
        raise TypeError(f"Error instantiating {ret_type.__name__} with kwargs {kwargs}: {e}") from e
