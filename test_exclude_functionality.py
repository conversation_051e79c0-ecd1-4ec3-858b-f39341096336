#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify the exclude functionality in YtdlPlaylistRecon.get_video_list method.
"""

import json
import os
from mockito import ANY, mock, unstub, when
from typing import List, Optional, Tuple, Union
from urllib3.util import parse_url, Url

from src.youtube_recon.impl.ytdlp_playlist_recon import YtdlPlaylistRecon
from src.youtube_recon.model.ytdlp import VideoDataByYtDlp
import src.youtube_recon.util as util


def create_url(url_str: str) -> Url:
    """Helper function to create Url objects"""
    return parse_url(url_str)


def test_exclude_functionality():
    """Test the exclude functionality in YtdlPlaylistRecon.get_video_list"""
    
    # Load sample data
    sample_json_path = os.path.join('tests', 'resource', 'ytdlp.playlist.3.sample.json')
    with open(sample_json_path, 'r') as f:
        sample_playlist_data = json.load(f)
    
    # Extract video IDs from sample data for testing
    video_ids = []
    for entry in sample_playlist_data['entries']:
        if entry is not None:
            video_ids.append(entry.get('id'))
    
    print(f"Found {len(video_ids)} videos in sample playlist:")
    for i, vid_id in enumerate(video_ids):
        print(f"  {i}: {vid_id}")
    
    # Mock the YoutubeDL instance
    mock_ydl = mock()
    when(mock_ydl).extract_info(ANY, download=False).thenReturn(sample_playlist_data)
    when(mock_ydl).get_playlist_meta(playlist_url=ANY).thenReturn(sample_playlist_data)
    
    # Mock the util.create_model_instance_recursive function
    def mock_create_model_instance(ret_type, fields_data):
        # Create a simplified VideoDataByYtDlp instance for testing
        return VideoDataByYtDlp(
            categories=fields_data.get('categories', []),
            channel=fields_data.get('channel', ''),
            description=fields_data.get('description', ''),
            duration=fields_data.get('duration', 0),
            fulltitle=fields_data.get('fulltitle', ''),
            id=fields_data.get('id', ''),
            tags=fields_data.get('tags', []),
            thumbnail=fields_data.get('thumbnail', ''),
            timestamp=fields_data.get('timestamp', 0),
            uploader=fields_data.get('uploader', ''),
            uploader_url=fields_data.get('uploader_url', ''),
            webpage_url=fields_data.get('webpage_url', '')
        )
    
    when(util).create_model_instance_recursive(ret_type=ANY, fields_data=ANY).thenCall(mock_create_model_instance)
    
    # Create YtdlPlaylistRecon instance
    playlist_recon = YtdlPlaylistRecon(ytdl=mock_ydl)
    
    # Test URL
    test_url = create_url("https://www.youtube.com/playlist?list=test_playlist")
    
    try:
        # Test 1: No exclusions - should return all videos
        print("\n=== Test 1: No exclusions ===")
        result_no_exclude = playlist_recon.get_video_list(playlist_url=test_url)
        print(f"Result count: {len(result_no_exclude)}")
        print("Video IDs returned:")
        for video in result_no_exclude:
            print(f"  - {video.id}")
        
        # Test 2: Exclude single video (string)
        print("\n=== Test 2: Exclude single video (string) ===")
        exclude_single = video_ids[0] if video_ids else "test_id"
        print(f"Excluding: {exclude_single}")
        result_exclude_single = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_single)
        print(f"Result count: {len(result_exclude_single)}")
        print("Video IDs returned:")
        for video in result_exclude_single:
            print(f"  - {video.id}")
        
        # Verify the excluded video is not in the result
        excluded_ids = [video.id for video in result_exclude_single]
        assert exclude_single not in excluded_ids, f"Excluded video {exclude_single} should not be in result"
        print(f"✓ Successfully excluded {exclude_single}")
        
        # Test 3: Exclude multiple videos (tuple)
        print("\n=== Test 3: Exclude multiple videos (tuple) ===")
        exclude_multiple = tuple(video_ids[:2]) if len(video_ids) >= 2 else ("test_id1", "test_id2")
        print(f"Excluding: {exclude_multiple}")
        result_exclude_multiple = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_multiple)
        print(f"Result count: {len(result_exclude_multiple)}")
        print("Video IDs returned:")
        for video in result_exclude_multiple:
            print(f"  - {video.id}")
        
        # Verify the excluded videos are not in the result
        excluded_ids = [video.id for video in result_exclude_multiple]
        for excluded_id in exclude_multiple:
            assert excluded_id not in excluded_ids, f"Excluded video {excluded_id} should not be in result"
        print(f"✓ Successfully excluded {exclude_multiple}")
        
        # Test 4: Exclude non-existent video
        print("\n=== Test 4: Exclude non-existent video ===")
        exclude_nonexistent = "non_existent_video_id"
        print(f"Excluding: {exclude_nonexistent}")
        result_exclude_nonexistent = playlist_recon.get_video_list(playlist_url=test_url, exclude=exclude_nonexistent)
        print(f"Result count: {len(result_exclude_nonexistent)}")
        # Should return same count as no exclusions since the video doesn't exist
        assert len(result_exclude_nonexistent) == len(result_no_exclude), "Non-existent exclusion should not affect result count"
        print("✓ Non-existent video exclusion handled correctly")
        
        print("\n🎉 All tests passed! Exclude functionality is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise
    finally:
        # Clean up mocks
        unstub()


if __name__ == "__main__":
    test_exclude_functionality()
