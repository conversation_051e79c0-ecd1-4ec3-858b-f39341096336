#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to validate the SharedLock implementation in QuotaSupervisor.
This test verifies that sync and async operations can properly coordinate
and that timeout functionality works correctly.
"""

import asyncio
import logging
import time
import threading
import sys
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our fixed version
sys.path.insert(0, os.path.dirname(__file__))
from quota_supervisor_fixed import QuotaSupervisor


async def test_shared_lock_coordination():
    """Test that sync and async operations properly coordinate through SharedLock"""
    logger.info("=== Shared Lock Coordination Test ===")
    
    quota_supervisor = QuotaSupervisor(max_cap=5, time_frame=10, lock_timeout=5.0)
    results = []
    
    def sync_worker(worker_id: int):
        """Synchronous worker that uses quota supervisor"""
        try:
            with quota_supervisor.consume(rate_limit=1):
                logger.info(f"Sync worker {worker_id}: got quota")
                time.sleep(0.2)  # Simulate work
                logger.info(f"Sync worker {worker_id}: completed")
                results.append(f"sync_success_{worker_id}")
        except Exception as e:
            logger.error(f"Sync worker {worker_id} failed: {e}")
            results.append(f"sync_error_{worker_id}")
    
    async def async_worker(worker_id: int):
        """Asynchronous worker that uses quota supervisor"""
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info(f"Async worker {worker_id}: got quota")
                await asyncio.sleep(0.2)  # Simulate async work
                logger.info(f"Async worker {worker_id}: completed")
                results.append(f"async_success_{worker_id}")
        except Exception as e:
            logger.error(f"Async worker {worker_id} failed: {e}")
            results.append(f"async_error_{worker_id}")
    
    # Create mixed sync and async tasks
    tasks = []
    
    # Add sync tasks (run in threads)
    for i in range(3):
        task = asyncio.create_task(asyncio.to_thread(sync_worker, i))
        tasks.append(task)
    
    # Add async tasks
    for i in range(3, 6):
        task = asyncio.create_task(async_worker(i))
        tasks.append(task)
    
    start_time = time.time()
    
    try:
        # Wait for all tasks
        await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=30.0
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Mixed operations completed in {duration:.2f}s")
        logger.info(f"Results: {results}")
        
        # Check results
        successful = sum(1 for r in results if "success" in r)
        failed = sum(1 for r in results if "error" in r)
        
        logger.info(f"Successful: {successful}, Failed: {failed}")
        
        if successful >= 5:  # At least 5 out of 6 should succeed
            logger.info("✅ Shared lock coordination test PASSED")
            return True
        else:
            logger.error("❌ Shared lock coordination test FAILED")
            return False
            
    except asyncio.TimeoutError:
        logger.error("❌ Shared lock coordination test TIMED OUT")
        return False


async def test_timeout_functionality():
    """Test that timeout functionality works correctly"""
    logger.info("=== Timeout Functionality Test ===")
    
    # Create quota supervisor with short timeout
    quota_supervisor = QuotaSupervisor(max_cap=1, time_frame=10, lock_timeout=2.0)
    
    async def long_running_task():
        """Task that holds the lock for a long time"""
        async with quota_supervisor.async_consume(rate_limit=1):
            logger.info("Long task: holding lock for 5 seconds")
            await asyncio.sleep(5.0)
            return "long_success"
    
    async def quick_task():
        """Task that should timeout"""
        await asyncio.sleep(0.5)  # Let long task acquire lock first
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info("Quick task: got lock")
                return "quick_success"
        except asyncio.TimeoutError:
            logger.info("Quick task: timed out as expected")
            return "quick_timeout"
    
    # Start both tasks
    long_task = asyncio.create_task(long_running_task())
    quick_task = asyncio.create_task(quick_task())
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(long_task, quick_task, return_exceptions=True),
            timeout=10.0
        )
        
        logger.info(f"Timeout test results: {results}")
        
        # Check that long task succeeded and quick task timed out
        long_result, quick_result = results
        
        if (isinstance(long_result, str) and long_result == "long_success" and
            isinstance(quick_result, str) and quick_result == "quick_timeout"):
            logger.info("✅ Timeout functionality test PASSED")
            return True
        else:
            logger.error("❌ Timeout functionality test FAILED")
            return False
            
    except asyncio.TimeoutError:
        logger.error("❌ Timeout functionality test TIMED OUT")
        return False


async def test_quota_management():
    """Test that quota management works correctly with SharedLock"""
    logger.info("=== Quota Management Test ===")
    
    quota_supervisor = QuotaSupervisor(max_cap=3, time_frame=2, lock_timeout=5.0)
    
    async def quota_worker(worker_id: int):
        """Worker that consumes quota"""
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info(f"Quota worker {worker_id}: got quota")
                await asyncio.sleep(0.1)
                logger.info(f"Quota worker {worker_id}: completed")
                return f"quota_success_{worker_id}"
        except Exception as e:
            logger.error(f"Quota worker {worker_id} failed: {e}")
            return f"quota_error_{worker_id}"
    
    # Create 6 workers (more than quota capacity)
    tasks = [asyncio.create_task(quota_worker(i)) for i in range(6)]
    
    start_time = time.time()
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=15.0
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Quota management test completed in {duration:.2f}s")
        logger.info(f"Results: {results}")
        
        # Check that all workers completed (some after quota reset)
        successful = sum(1 for r in results if isinstance(r, str) and "success" in r)
        
        if successful == 6:
            logger.info("✅ Quota management test PASSED")
            return True
        else:
            logger.error("❌ Quota management test FAILED")
            return False
            
    except asyncio.TimeoutError:
        logger.error("❌ Quota management test TIMED OUT")
        return False


async def main():
    """Run all SharedLock implementation tests"""
    logger.info("Starting SharedLock implementation tests")
    
    tests = [
        ("Shared Lock Coordination", test_shared_lock_coordination),
        ("Timeout Functionality", test_timeout_functionality),
        ("Quota Management", test_quota_management),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 ALL SHARED LOCK TESTS PASSED!")
        logger.info("The SharedLock implementation is working correctly.")
        logger.info("✅ Sync and async operations coordinate properly")
        logger.info("✅ Timeout functionality works as expected")
        logger.info("✅ Quota management works with SharedLock")
        return True
    else:
        logger.warning(f"\n⚠️  {total - passed} test(s) failed.")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Tests failed with exception: {e}")
        exit(1)
