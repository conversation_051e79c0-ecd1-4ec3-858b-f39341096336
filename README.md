# YouTube Recon

A flexible and extensible Python framework for extracting and analyzing metadata from YouTube videos and playlists, with demo implementations including Google's Gemini AI integration.

## Overview

YouTube Recon is a lightweight and flexible framework designed to help developers easily build tools for obtaining and processing YouTube video metadata. The core design philosophy emphasizes flexibility and extensibility, allowing for easy adaptation to different tools and AI models.

The framework provides a solid foundation for:
- Extracting video properties and metadata
- Processing YouTube playlists
- Analyzing video content with AI models

It includes demonstration implementations for:
- Generating video summaries using AI model (Gemini)
- Handling video transcripts
- Extracting key points and themes from videos

## Key Features

- **Modular Architecture**: Core functionality is abstracted through base classes that can be extended for specific use cases
- **Asynchronous Support**: Efficient processing of multiple videos using async/await patterns
- **Playlist Support**: Process entire playlists with parallel processing capabilities
- **Quota Management**: Built-in rate limiting and quota supervision for API calls
- **CLI Interface**: Command-line tools for easy interaction with videos and playlists
- **Flexible Output Formats**: Support for both text and JSON output formats
- **Error Handling**: Robust handling of deleted videos and other edge cases
- **AI Integrations**: Ready-to-use implementation with:
  - Google's Gemini AI for video content analysis

## Base Components

- **VideoReconBase**: The abstract base class that defines the core interface for video metadata extraction and processing
- **VideoReconCLIBase**: Base class for command-line interface implementations
- **Pydantic Models**: Type-safe data models for video metadata and summaries
- **Utility Modules**: URL handling, configuration management, and other helper functions

## Implementations

- **GeminiVideoRecon**: Implementation that uses Google's Gemini AI for analyzing video content
- **VideoDataByYtDlp**: A Pydantic model for structured video metadata from yt-dlp
- **VideoSummary**: A Pydantic model for AI-generated video summaries

## Getting Started

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/youtube-recon.git
cd youtube-recon

# Install dependencies
pip install -e .
```

### Basic Usage

#### Using the Gemini Implementation

```python
from urllib3.util import parse_url
from confijector.config_loader import ConfigLoader, config_bind_factory
from confijector.config_parser import ConfigParser, ConfigParserModule
from injector import Injector
from youtube_recon.impl.gemini_video_recon import GeminiVideoRecon, gen_gemini_video_recon_injector

# Load configuration
configs = [ConfigLoader.get_default_conf_file()]

# Set up dependency injection
injector_obj = Injector(modules=[
    config_bind_factory(conf_files=configs),
    ConfigParserModule()
])

# Get Gemini implementation
injector_obj = gen_gemini_video_recon_injector(injector_obj=injector_obj)
gemini_recon = injector_obj.get(GeminiVideoRecon)

# Get video summary
url = parse_url("https://www.youtube.com/watch?v=your_video_id")
video_props, video_summary = gemini_recon.get_video_meta(url=url)

# Print summary
print(f"Summary: {video_summary.summary}")
print(f"Main Theme: {video_summary.main_theme}")
print("Key Points:")
for point in video_summary.key_points:
    print(f"- {point}")
```

#### Using the CLI

```bash
# Get summary for a single video
python -m youtube_recon.impl.gemini_video_recon_cli video summary -u https://www.youtube.com/watch?v=your_video_id

# Get detailed information for a video
python -m youtube_recon.impl.gemini_video_recon_cli video detail -u https://www.youtube.com/watch?v=your_video_id

# Get summaries for all videos in a playlist
python -m youtube_recon.impl.gemini_video_recon_cli playlist summaries -u https://www.youtube.com/playlist?list=your_playlist_id

# Output in JSON format
python -m youtube_recon.impl.gemini_video_recon_cli video summary -u https://www.youtube.com/watch?v=your_video_id -f json

# Save output to a file
python -m youtube_recon.impl.gemini_video_recon_cli video summary -u https://www.youtube.com/watch?v=your_video_id -p output.txt
```

## Extending the Framework

YouTube Recon is designed to be easily extended with new AI model integrations or custom processing logic:

1. Create a new class that inherits from `VideoReconBase`
2. Implement the required abstract methods
3. Optionally create a CLI implementation by extending `VideoReconCLIBase`

See the existing implementations (GeminiVideoRecon) for examples.

## Configuration

The framework uses JSON configuration files for settings. Example configuration structure:

```json
{
  "youtube_recon": {
    "gemini": {
      "api_key": "your_gemini_api_key",
      "model": "gemini-2.0-flash-lite",
      "quota": {
        "gemini-2.0-flash-lite": "30RPM"
      }
    },
    "cli": {
      "ytdlp": {
        "hide": ["language", "thumbnail", "web_url"]
      }
    }
  }
}
```

## License

Apache-2.0

## Third-Party Licenses

This project depends on several third-party libraries, each with their own licenses:

- **python-dotenv**: BSD 3-Clause License
- **yt-dlp**: Unlicense
- **icecream**: MIT License
- **tqdm**: MIT License
- **mockito**: MIT License
- **litellm**: MIT License
- **click**: BSD 3-Clause License
- **google-genai**: Apache License 2.0
- **pydantic**: MIT License
- **injector**: BSD 3-Clause License
- **logear**: Apache License 2.0 (custom library)
- **confijector**: Apache License 2.0 (custom library)
- **quota-supervisor**: Apache License 2.0 (custom library)

These dependencies might have additional requirements or restrictions. Please refer to their respective license files for more information.
