#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test to verify the QuotaSupervisor deadlock fix works.
This test focuses on the core issue without complex scenarios.
"""

import asyncio
import logging
import time
import sys
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our fixed version
sys.path.insert(0, os.path.dirname(__file__))
from quota_supervisor_fixed import QuotaSupervisor


async def simple_worker(worker_id: int, quota_supervisor: QuotaSupervisor, work_duration: float = 0.1):
    """Simple worker that uses quota supervisor"""
    logger.info(f"Worker {worker_id} starting")
    
    try:
        async with quota_supervisor.async_consume(rate_limit=1):
            logger.info(f"Worker {worker_id} got quota, working for {work_duration}s")
            await asyncio.sleep(work_duration)
            logger.info(f"Worker {worker_id} completed work")
            return f"success_{worker_id}"
    except Exception as e:
        logger.error(f"Worker {worker_id} failed: {e}")
        return f"error_{worker_id}"


async def test_basic_concurrency():
    """Test basic concurrent access to quota supervisor"""
    logger.info("=== Basic Concurrency Test ===")
    
    # Create quota supervisor with shorter time frame for testing
    quota_supervisor = QuotaSupervisor(max_cap=10, time_frame=5)
    
    # Create 20 workers (more than quota to test queuing)
    num_workers = 20
    tasks = []
    
    for i in range(num_workers):
        task = asyncio.create_task(simple_worker(i, quota_supervisor, work_duration=0.1))
        tasks.append(task)
    
    start_time = time.time()
    
    try:
        # Wait for all tasks with reasonable timeout
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=30.0
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, str) and r.startswith("success"))
        failed = sum(1 for r in results if isinstance(r, str) and r.startswith("error"))
        exceptions = sum(1 for r in results if isinstance(r, Exception))
        
        logger.info(f"Test completed in {duration:.2f}s")
        logger.info(f"Results: {successful} successful, {failed} failed, {exceptions} exceptions")
        
        # Success criteria: most workers should complete, no deadlock
        if duration < 25.0 and successful > num_workers * 0.5:  # At least 50% success
            logger.info("✅ Basic concurrency test PASSED")
            return True
        else:
            logger.error("❌ Basic concurrency test FAILED")
            return False
            
    except asyncio.TimeoutError:
        logger.error("❌ Basic concurrency test TIMED OUT - possible deadlock")
        return False


async def test_timeout_behavior():
    """Test that timeout behavior works correctly"""
    logger.info("=== Timeout Behavior Test ===")
    
    # Create quota supervisor with limited capacity and short time frame
    quota_supervisor = QuotaSupervisor(max_cap=1, time_frame=3)
    
    async def long_worker():
        """Worker that holds the lock for a long time"""
        async with quota_supervisor.async_consume(rate_limit=1):
            logger.info("Long worker: holding lock for 5 seconds")
            await asyncio.sleep(5.0)
            return "long_success"
    
    async def quick_worker():
        """Worker that should timeout"""
        await asyncio.sleep(0.5)  # Let long worker get the lock first
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info("Quick worker: got lock")
                return "quick_success"
        except asyncio.TimeoutError:
            logger.info("Quick worker: timed out as expected")
            return "quick_timeout"
    
    # Start both workers
    long_task = asyncio.create_task(long_worker())
    quick_task = asyncio.create_task(quick_worker())
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(long_task, quick_task, return_exceptions=True),
            timeout=10.0
        )
        
        logger.info(f"Timeout test results: {results}")
        
        # Check that quick worker timed out and long worker succeeded
        long_result, quick_result = results
        
        if (isinstance(long_result, str) and long_result == "long_success" and
            isinstance(quick_result, str) and quick_result == "quick_timeout"):
            logger.info("✅ Timeout behavior test PASSED")
            return True
        else:
            logger.error("❌ Timeout behavior test FAILED")
            return False
            
    except asyncio.TimeoutError:
        logger.error("❌ Timeout behavior test TIMED OUT")
        return False


async def test_async_only():
    """Test async operations only (sync operations disabled for now)"""
    logger.info("=== Async Only Test ===")

    quota_supervisor = QuotaSupervisor(max_cap=5, time_frame=2)

    async def async_worker(worker_id: int):
        """Asynchronous worker"""
        try:
            async with quota_supervisor.async_consume(rate_limit=1):
                logger.info(f"Async worker {worker_id}: got quota")
                await asyncio.sleep(0.1)
                logger.info(f"Async worker {worker_id}: completed")
                return f"async_success_{worker_id}"
        except Exception as e:
            logger.error(f"Async worker {worker_id} failed: {e}")
            return f"async_error_{worker_id}"

    # Run multiple async workers
    tasks = [asyncio.create_task(async_worker(i)) for i in range(3)]

    try:
        results = await asyncio.wait_for(
            asyncio.gather(*tasks, return_exceptions=True),
            timeout=10.0
        )

        logger.info(f"Async only test results: {results}")

        # Check that all completed successfully
        successful = sum(1 for r in results if isinstance(r, str) and "success" in r)
        if successful == len(tasks):
            logger.info("✅ Async only test PASSED")
            return True
        else:
            logger.error("❌ Async only test FAILED")
            return False

    except asyncio.TimeoutError:
        logger.error("❌ Async only test TIMED OUT")
        return False


async def main():
    """Run all simple tests"""
    logger.info("Starting simple deadlock tests")
    
    tests = [
        ("Basic Concurrency", test_basic_concurrency),
        ("Timeout Behavior", test_timeout_behavior),
        ("Async Only", test_async_only),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 ALL SIMPLE TESTS PASSED!")
        logger.info("The QuotaSupervisor fix is working correctly.")
        return True
    else:
        logger.warning(f"\n⚠️  {total - passed} test(s) failed.")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Tests failed with exception: {e}")
        exit(1)
