[project]
name = "youtube-recon"
version = "0.1.0"
description = ""
authors = [
    {name = "Art",email = "<EMAIL>"}
]
license = "Apache-2.0"
readme = "README.md"
packages = [{include = "youtube_recon", from = "src"}]
requires-python = ">=3.10,<4.0"
dependencies = [
    "python-dotenv (>=1.0.1,<2.0.0)",
    "yt-dlp (>=2025.3.21,<2026.0.0)",
    "icecream (>=2.1.4,<3.0.0)",
    "tqdm (>=4.67.1,<5.0.0)",
    "mockito (>=1.5.4,<2.0.0)",
    "litellm (>=1.65.3,<2.0.0)",
    "click (>=8.1.8,<9.0.0)",
    "google-genai (>=1.11.0,<2.0.0)",
    "logear @ git+ssh://**************/artingitlab/logear.git",
    "confijector @ git+ssh://**************/artingitlab/confijector.git",
    "quota-supervisor @ git+ssh://**************/artingitlab/quota_supervisor.git",
    "pydantic (>=2.11.4,<3.0.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
ipython = "8.34.0"
jupyterlab = "^4.3.6"
pytest = "^8.3.5"
pytest-asyncio = "^0.26.0"
mockito = "^1.5.4"

[tool.pytest.ini_options]
testpaths = "tests"
asyncio_default_fixture_loop_scope = "function"
