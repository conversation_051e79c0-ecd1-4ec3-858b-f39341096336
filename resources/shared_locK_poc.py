import threading
import asyncio
import functools

class SharedLock:
    def __init__(self):
        self._lock = threading.Lock()

    # Synchronous context manager support
    def __enter__(self):
        self._lock.acquire()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._lock.release()

    # Asynchronous context manager support
    async def __aenter__(self):
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, self._lock.acquire)
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        # Release directly (fast and non-blocking)
        self._lock.release()
        # Alternatively, if you want to run release in an executor too:
        # loop = asyncio.get_running_loop()
        # await loop.run_in_executor(None, self._lock.release)

# Example usage:

# Synchronous example
def sync_function(shared_lock):
    with shared_lock:
        print("Sync code holding the lock")
        # Do some work...

# Asynchronous example
async def async_coroutine(shared_lock):
    async with shared_lock:
        print("Async code holding the lock")
        await asyncio.sleep(1)  # Simulate async work
        # Do some async work...

# To test it together (run in an async environment)
async def main():
    shared_lock = SharedLock()
    
    # Run sync in a thread
    threading.Thread(target=sync_function, args=(shared_lock,)).start()
    
    # Run async
    await async_coroutine(shared_lock)

if __name__ == "__main__":
    asyncio.run(main())
