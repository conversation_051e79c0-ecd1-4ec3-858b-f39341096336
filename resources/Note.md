- About __main__.VideoReconCLI_0x7bf8db2e49d.qgzhg7lx.excerpts.log file
	It was excerpt outputs by grep with 'field_name:|data_dict\[ic\(field_name\)\]:' regex on __main__.VideoReconCLI_0x7bf8db2e49d.qgzhg7lx.log file. 
	And it supposed to pick up outputs by below icecream.ic statements in the create_model_instance_recursive function in the util.py file.

	```
	$ awk 'BEGIN{flag=0} /^[ ]*def create_model_instance_recursive/{flag=1; print $0; next} {if(flag==1) {print $0}}' ./util.py  | grep -E 'ic\(|[ ]+for[ ]+' | grep -v -E '^[ ]+#'
	            name for name, field_info in model_fields.items() if field_info.is_required()
	    for field_name in fields_to_process:
	        field_value = ic(data_dict[ic(field_name)])
	        field_info = ic(model_fields[field_name])
	        field_type_hint = ic(field_info.annotation)
	        origin = ic(get_origin(field_type_hint))
	        args = ic(get_args(field_type_hint))
	        nested_model_type = ic(_get_base_model_type(field_type_hint))
	                    for item in field_value:
	                    for key, value in field_value.items():
	```
